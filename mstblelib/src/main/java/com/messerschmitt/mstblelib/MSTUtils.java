package com.messerschmitt.mstblelib;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import org.json.JSONArray;
import org.json.JSONException;

import java.io.ByteArrayInputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;
import java.util.UUID;

/**
 *
 * provides Helper methods
 *
 * <AUTHOR> Systems GmbH
 * @version 2020.1001
 */
class MSTUtils {
    private static final String TAG = "SMARTKEY";
    public static final byte[][] dhAuthStaticValues = new byte[][] {{0x07,0x00,0x00,0x00},{0x07,0x00,0x00,0x00},{(byte)0xB8,(byte)0x42,(byte)0x78,(byte)0xDC},{(byte)0x9B,(byte)0x82,(byte)0x51,(byte)0x54}};

    private static final String MSTPrivateServiceUUIDString = "2735E1EB-ABCE-4B76-92B3-0E0F11B26C0C";
    private static final String MSTReadCharacteristicUUIDString   = "11111111-FBA8-4D5E-AF4A-F63544894D46";
    private static final String MSTWriteCharacteristicUUIDString  = "00000000-FA05-4F0F-8275-************";
    private static final String MSTReadNotifyDescriptorUUIDString ="00002902-0000-1000-8000-00805f9b34fb";
    public static final UUID MSTPrivateServiceUUID = UUID.fromString(MSTPrivateServiceUUIDString);
    public static final UUID MSTReadCharacteristicUUID = UUID.fromString(MSTReadCharacteristicUUIDString);
    public static final UUID MSTNotifyDescriptorUUID = UUID.fromString(MSTReadNotifyDescriptorUUIDString);
    public static final UUID MSTWriteCharacteristicUUID = UUID.fromString(MSTWriteCharacteristicUUIDString);

    public static Bitmap ByteArrayToBitmap(byte[] byteArray){
        ByteArrayInputStream arrayInputStream = new ByteArrayInputStream(byteArray);
        Bitmap bitmap = BitmapFactory.decodeStream(arrayInputStream);
        return bitmap;
    }

    public static Date parseDateTime(String dateString){
        if(dateString == null) return null;
        if (dateString.contains("T")) dateString = dateString.replace('T',' ');

        try{
            DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            return fmt.parse(dateString);
        } catch (ParseException e) {
            //Could not parse datetime
            return null;
        }
    }

    public static String iso8601fromDate(Date date){
        DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd'T'HH:mmXXX");

        return fmt.format(date);
    }

    public static byte[] getByteArrayFromJsonArray(JSONArray jsonArray){
        byte[] result = new byte[jsonArray.length()];

        try {
            for (int i = 0; i < jsonArray.length(); i++) {
                int bi = (int) jsonArray.get(i);
                result[i] = (byte) bi;
            }
        } catch (JSONException e) {
            //JSON Exception
        }
        return result;
    }

    private final static char[] hexArray = "0123456789ABCDEF".toCharArray();

    public static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for ( int j = 0; j < bytes.length; j++ ) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
    }

    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len/2];
        for(int i = 0; i < len; i+=2){

            data[i/2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i+1), 16));
        }

        return data;
    }

    public static String byteArrToTxtString(byte[] data){
        String result;
        Integer i;
        Integer p;
        p = 0;
        if (data == null) return ("");

        for(i = 0;i<data.length;i++){
              if((data[i]<0x20)||(data[i]==0xFF)) break;
              p++;
        }

        StringBuilder sb = new StringBuilder();
        sb.append("");
        for (i = 0; i<p; i++){
            sb.append((char) data[i]);
        }

        result = sb.toString();

        return result;
    }

    public static String md5(final String s) {
        final String MD5 = "MD5";
        try {
            MessageDigest digest = java.security.MessageDigest
                    .getInstance(MD5);
            digest.update(s.getBytes());
            byte[] messageDigest = digest.digest();

            StringBuilder hexString = new StringBuilder();
            for (byte aMessageDigest : messageDigest) {
                String h = Integer.toHexString(0xFF & aMessageDigest);
                while (h.length() < 2)
                    h = "0" + h;
                hexString.append(h);
            }
            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

//__________________________________________________________

    //******************************************************************************

    public static long ByteAsLong(final byte b)
    {
        long Result;

        Result = b;
        if(Result < 0){Result = Result & 0xFF;}

        return(Result);
    }

    //******************************************************************************

    private static byte LongAsByte(final long l)
    {
        byte Result;

        Result = (byte) l;

        return(Result);
    }

    //******************************************************************************

    public static int ByteAsInt(final byte b)
    {
        int Result;

        Result = b;
        if(Result < 0){Result = Result & 0xFF;}

        return(Result);
    }

    //******************************************************************************

    public static byte IntAsByte(final int i)
    {
        byte Result;

        Result = (byte) i;

        return(Result);
    }

    //******************************************************************************

    public static int BytesToWordLH(byte[] b, int o) {
        return(ByteAsInt(b[o])+(ByteAsInt(b[o+1])<<8));
    }

    //*******************************************************************************

    private static long BytesToDWordLH(byte[] b,int o) {
        return(ByteAsLong(b[o]) + (ByteAsLong(b[o+1])<<8) + (ByteAsLong(b[o+2])<<16) + (ByteAsLong(b[o+3])<<24));
    }
    //*******************************************************************************

    private static long dwordPowMod2p32s1(long base, long exp) { // "result" := ("base" ^ "exp") mod "(2^31)-1"
        long X = exp;
        long D = 1;                          // D = 1;
        long B = base % 0x7fffffff;          // B = b % M;

        if ((X & 0x01) == 0x01) D = B;       // if((X & 1) == 1) D = B;

        while (X > 1) {                      // while(X > 1)
            X >>= 1;                         // X >>= 1;

            B *= B; B %= 0x7fffffff;         // B = (B * B) % M;

            if ((X & 0x01) == 0x01) {        // if ((X & 1) == 1)
                D *= B; D %= 0x7fffffff;     // D = (D * B) % M;
            }
        }
        return (D);                          // return(D);
    }

    //*******************************************************************************

    private static byte[] DH32random() { // create "random" in range[1..0x7ffffffe]
        byte[] res = new byte[4];

        res[0] = IntAsByte(new Random().nextInt((256)));
        res[1] = IntAsByte(new Random().nextInt((256)));
        res[2] = IntAsByte(new Random().nextInt((256)));
        res[3] = IntAsByte(new Random().nextInt((128)));

        if((res[0]==0x00) && (res[1]==0x00) && (res[2]==0x00) && (res[3]==0x00)) res[0] = IntAsByte(0x01); // ? "0" -> "1"
        if((res[0]==0xff) && (res[1]==0xff) && (res[2]==0xff) && (res[3]==0x7f)) res[0] = IntAsByte(0xfe); // ? "max" -> "max-1"

        return(res);
    }

    //*******************************************************************************

    public static byte[] DH32powmod(byte[] base, byte[] privat) { // calc "value" with "base" and "privat"
        byte[] res = new byte[4];
        long r;

        r = dwordPowMod2p32s1(BytesToDWordLH(base, 0), BytesToDWordLH(privat, 0));

        res[0]=LongAsByte(r&0xff);
        res[1]=LongAsByte((r>>8)&0xff);
        res[2]=LongAsByte((r>>16)&0xff);
        res[3]=LongAsByte((r>>24)&0xff);

        return(res);
    }

    //******************************************************************************

    public static int MSTbleCalcCRC16(int start, byte[] buf, int cnt, int off)
    {
        int i,j;
        int Result=start;

        for(i=0;i<cnt;i++)
        {
            Result ^= ByteAsInt(buf[off+i]);

            for(j=0;j<8;j++)
            {
                if((Result & 0x0001)==0x0001)
                {
                    Result >>= 1;
                    Result ^= 0x8408; // x^16 + x^12 + x^5 + 1
                }
                else
                {
                    Result >>= 1;
                }
            }
        }

        Result ^= 0xffff;
        Result &= 0xffff;

        return(Result);
    }

//******************************************************************************
// DeviceChallenge[16]ì als StepByStep f¸r "APP-Decode&Check-BLEv1xì:
//******************************************************************************
    public static boolean DecodeDeviceChallenge(MSTBleDevice dev, byte[] deviceChallenge)
    {
        int i;
        for (i = 0 ; i < 16 ; i++){dev.authbuf[i] = deviceChallenge[i];}

        dev.AuthMode = 0;
        if(dev.authbuf.length!=16) return(false);
        if((ByteAsInt(dev.authbuf[0]) & 0xEF) != 0x01) return(false);  // "successfull connected and ready"
        if((ByteAsInt(dev.authbuf[1]) & 0x13) < 0x02) return(false);

        dev.CRCauth0 = dev.getmDeviceSystemCode() & 0x03ff;
        dev.CRCauth1 = BytesToWordLH(dev.authbuf,14);
        if(dev.CRCauth1!=MSTbleCalcCRC16(dev.CRCauth0, dev.authbuf, 14,0)) {
            return(false);}

        for(i=0;i<6;i++) dev.DevMAC[i]=dev.authbuf[2+i];
        for(i=0;i<6;i++) dev.DevDT[i]=dev.authbuf[8+i];

        if((ByteAsInt(dev.authbuf[1]) & 0x10) == 0x10) dev.AuthMode=3;
        else if((ByteAsInt(dev.authbuf[1]) & 0x12) == 0x02) dev.AuthMode=2;

        return(dev.AuthMode>1);
    }

    //******************************************************************************
    // AppResponseChallenge[16]ì als StepByStep für "APP-Encode-BLE":
    //******************************************************************************
    public static void EncodeAppResponseChallengeL2(MSTBleDevice dev)
    {
        java.util.Calendar calendar = java.util.GregorianCalendar.getInstance();

        dev.AuthMode=2;
        dev.AppBleHL=0x12;
        dev.SessionKey[0]=IntAsByte(new Random().nextInt(256));
        dev.SessionKey[1]=IntAsByte(new Random().nextInt(256));

        dev.authbuf[0]=0x01;
        dev.authbuf[1]=0x02;
        dev.authbuf[2]=dev.AppType;
        dev.authbuf[3]=dev.AppVerHL;
        dev.authbuf[4]=dev.AppBleHL;
        dev.authbuf[5]=dev.AppFlags;
        dev.authbuf[6]=IntAsByte(calendar.get(Calendar.DAY_OF_MONTH));
        dev.authbuf[7]=IntAsByte(calendar.get(Calendar.MONTH));
        dev.authbuf[8]=IntAsByte((calendar.get(Calendar.YEAR) % 100));
        dev.authbuf[9]=IntAsByte(calendar.get(Calendar.HOUR_OF_DAY));
        dev.authbuf[10]=IntAsByte(calendar.get(Calendar.MINUTE));
        dev.authbuf[11]=IntAsByte(calendar.get(Calendar.SECOND));
        dev.authbuf[12]=dev.SessionKey[0];
        dev.authbuf[13]=dev.SessionKey[1];

        dev.CRCauth2=MSTbleCalcCRC16(dev.CRCauth1, dev.authbuf,14,0);
        dev.authbuf[14]=IntAsByte(dev.CRCauth2&0xff);
        dev.authbuf[15]=IntAsByte(dev.CRCauth2>>8);
    }

    //******************************************************************************
// DeviceResponse[11]ì als StepByStep für "APP-Decode&Check-BLE":
//******************************************************************************
    public static boolean DecodeDeviceResponseL2(MSTBleDevice dev, byte[] deviceResponse)
    {
        int i;

        for (i = 0 ; i < 11 ; i++){dev.authbuf[i] = deviceResponse[i];}

        if((ByteAsInt(dev.authbuf[0])&0xEF)!=0x01) return(false);// "BLE Device ready"
        if((ByteAsInt(dev.authbuf[1])&0x0f)!=0x04)
        {
            return(false);
        }

        dev.CRCauth3=BytesToWordLH(dev.authbuf,9);
        if(dev.CRCauth3!=MSTbleCalcCRC16(dev.CRCauth2, dev.authbuf,9,0)){
            return(false);
        }

        dev.DevType=dev.authbuf[2];
        dev.DevVerHi=dev.authbuf[3];
        dev.DevVerLo=dev.authbuf[4];
        dev.DevBleHL=dev.authbuf[5];
        dev.DevFlags=dev.authbuf[6];
        dev.SessionKey[2]=dev.authbuf[7];
        dev.SessionKey[3]=dev.authbuf[8];
        dev.AuthMode=2;

        return(true);
    }

    //******************************************************************************
// "AppAuthResponseL3[16]" als StepByStep für "APP-Encode-Level3":
//******************************************************************************
    public static void EncodeAppResponseChallengeL3(MSTBleDevice dev) {
        java.util.Calendar calendar = java.util.GregorianCalendar.getInstance();

        dev.AuthMode = 3;
        dev.AppFlags = IntAsByte (0x03);

        dev.dhAuthRandomPrivat = DH32random();
        dev.dhAuthRandomPublic = DH32powmod(dhAuthStaticValues[1],dev.dhAuthRandomPrivat);
        dev.dhAuthSharedSecret = DH32powmod(dhAuthStaticValues[3],dev.dhAuthRandomPrivat);

        dev.dhSessionRandomPrivat = DH32random();
        dev.dhSessionRandomPublic = DH32powmod(dhAuthStaticValues[0],dev.dhSessionRandomPrivat);

        dev.authbuf[0] = IntAsByte(0x01);
        dev.authbuf[1] = IntAsByte(0x04);
        dev.authbuf[2] = dev.AppFlags;
        dev.authbuf[3] = IntAsByte(calendar.get(Calendar.DAY_OF_MONTH));
        dev.authbuf[4] = IntAsByte(calendar.get(Calendar.MONTH));
        dev.authbuf[5] = IntAsByte(calendar.get(Calendar.HOUR_OF_DAY));
        dev.authbuf[6] = IntAsByte(calendar.get(Calendar.MINUTE));
        for (int i = 0; i<4; i++) {
            dev.authbuf[7 + i] = dev.dhAuthRandomPublic[i];
            dev.authbuf[11 + i] = IntAsByte((ByteAsInt(dev.dhAuthSharedSecret[i]) ^ ByteAsInt(dev.dhSessionRandomPublic[i])));
        }

        dev.CRCauth2 = (((dev.CRCauth1>>8) & 0xff) ^ (dev.CRCauth1 & 0xff)) & 0xff;
        for (int i = 0; i<15; i++) dev.CRCauth2 ^= ByteAsInt(dev.authbuf[i]);
        dev.authbuf[15] = IntAsByte(dev.CRCauth2&0xff);
    }

    //******************************************************************************
// "DeviceAuthResponseL3[16]" als StepByStep für "APP-Decode&check-Level3":
//******************************************************************************
    public static boolean DecodeDeviceResponseL3(MSTBleDevice dev, byte[] deviceResponse) {
        dev.AuthMode = 0;

        if(deviceResponse.length!=16) return false;
        for (int i = 0 ; i < 16 ; i++) {dev.authbuf[i] = deviceResponse[i];}

        if((ByteAsInt(dev.authbuf[0])&0xEF)!=0x01) return false; // "BLE Device ready"
        if((ByteAsInt(dev.authbuf[1])&0x0f)!=0x06) {
            return(false);
        }

        for(int i = 0; i<4; i++) {
            dev.dhAuthExternPublic[i] = dev.authbuf[7+i];
            dev.dhAuthSessionExtern[i] = dev.authbuf[11+i];
        }

        dev.dhAuthExternSecret = DH32powmod(dev.dhAuthExternPublic,dhAuthStaticValues[2]);
        for(int i = 0; i<4; i++) {
            dev.dhSessionExternPublic[i] = IntAsByte((ByteAsInt(dev.dhAuthSessionExtern[i]) ^ ByteAsInt(dev.dhAuthExternSecret[i])));
        }
        dev.dhSessionSharedSecret = DH32powmod(dev.dhSessionExternPublic,dev.dhSessionRandomPrivat);

        dev.CRCauth3 = dev.CRCauth2 & 0xff;
        for(int i = 0; i<15; i++) dev.CRCauth3 ^= ByteAsInt(dev.authbuf[i]);
        for(int i = 0; i<4; i++) dev.CRCauth3 ^= ByteAsInt(dev.dhSessionSharedSecret[i]);
        if(dev.CRCauth3 != ByteAsInt(dev.authbuf[15])) return false;

        dev.DevType = dev.authbuf[2];
        dev.DevVerHi = dev.authbuf[3];
        dev.DevVerLo = dev.authbuf[4];
        dev.DevBleHL = dev.authbuf[5];
        dev.DevFlags = dev.authbuf[6];

        dev.dhBlockCipherRRCNT[0]=(byte)0x01;
        dev.dhBlockCipherRRCNT[1]=(byte)0x00;
        dev.dhBlockCipherRRCNT[2]=(byte)0x00;
        dev.dhBlockCipherRRCNT[3]=(byte)0x80;

        dev.AuthMode = 3;
        return true;
    }

    public static byte[] encodeBlockCipherL3(MSTBleDevice dev) {
        int[] blk = new int[2];
        byte[] CFBECB = new byte[16];

        blk[0]=BytesToWordLH(dev.dhSessionSharedSecret,0);
        blk[1]=BytesToWordLH(dev.dhSessionSharedSecret,2);
        blk[0]=MSTbleCalcCRC16(blk[0],dev.dhBlockCipherRRCNT,2,2);
        blk[1]=MSTbleCalcCRC16(blk[1],dev.dhBlockCipherRRCNT,2,0);

        CFBECB[0]=IntAsByte(ByteAsInt(dev.dhAuthSharedSecret[0]) ^ (blk[0] & 0xff));
        CFBECB[1]=IntAsByte(ByteAsInt(dev.dhAuthSharedSecret[1]) ^ ((blk[0] >> 8) & 0xff));
        CFBECB[2]=IntAsByte(ByteAsInt(dev.dhAuthSharedSecret[2]) ^ (blk[1] & 0xff));
        CFBECB[3]=IntAsByte(ByteAsInt(dev.dhAuthSharedSecret[3]) ^ ((blk[1] >> 8) & 0xff));
        for(int i=0;i<4;i++) {
            CFBECB[4+i]= IntAsByte(ByteAsInt(dev.dhSessionExternPublic[i]) ^ ByteAsInt(dev.dhSessionSharedSecret[i]));
            CFBECB[8+i]= IntAsByte(ByteAsInt(dev.dhSessionRandomPublic[i]) ^ ByteAsInt(dev.dhSessionSharedSecret[i]));
        }
        CFBECB[12]=IntAsByte(ByteAsInt(dev.dhAuthExternSecret[0]) ^ (blk[0] & 0xff));
        CFBECB[13]=IntAsByte(ByteAsInt(dev.dhAuthExternSecret[1]) ^ ((blk[0] >> 8) & 0xff));
        CFBECB[14]=IntAsByte(ByteAsInt(dev.dhAuthExternSecret[2]) ^ (blk[1] & 0xff));
        CFBECB[15]=IntAsByte(ByteAsInt(dev.dhAuthExternSecret[3]) ^ ((blk[1] >> 8) & 0xff));

        blk[0]=BytesToWordLH(dev.dhBlockCipherRRCNT,0);
        blk[1]=BytesToWordLH(dev.dhBlockCipherRRCNT,2);
        blk[0]=(blk[0]+1) & 0xffff;
        blk[1]=((blk[1] >> 1)&0xffff) + ((blk[1]<<15)&0xffff);
        dev.dhBlockCipherRRCNT[0]=IntAsByte(blk[0] & 0xff);
        dev.dhBlockCipherRRCNT[1]=IntAsByte((blk[0] >> 8) & 0xff);
        dev.dhBlockCipherRRCNT[2]=IntAsByte(blk[1] & 0xff);
        dev.dhBlockCipherRRCNT[3]=IntAsByte((blk[1] >> 8) & 0xff);

        return(CFBECB);
    }

//******************************************************************************
// MSTdoorKey(lang) BYTE [64]ì als StepByStep f¸r "Encode":
//******************************************************************************
    public static byte[] EncodeSendKeyData(byte[] key, MSTBleDevice dev) {
        byte[] result;
        int i;

        if (ByteAsInt(key[(key.length+8) & 0x0f]) < 0x8c) {
            result = new byte[48];
            for (i = 0; i < 48; i++) result[i] = 0;

            if ((key.length & 0x0f) == 0x00) {
                for (i = 0; i < 0x08; i++) result[0x08 + i] = key[0x08 + i];
                for (i = 0; i < 0x20; i++) result[0x10 + i] = key[0x10 + i];
            } else {
                for (i = 0; i < 0x08; i++) result[0x08 + i] = key[i];
                for (i = 0; i < 0x20; i++) result[0x10 + i] = key[0x08 + i];
            }
        } else {
            result = new byte[64];
            for (i = 0; i < 64; i++) result[i] = 0;

            if ((key.length & 0x0f) == 0x00) {
                for (i = 0; i < 0x08; i++) result[0x08 + i] = key[0x08 + i];
                for (i = 0; i < 0x20; i++) result[0x10 + i] = key[0x10 + i];
                for (i = 0; i < 0x10; i++) result[0x30 + i] = key[0x30 + i];
            } else {
                for (i = 0; i < 0x08; i++) result[0x08 + i] = key[i];
                for (i = 0; i < 0x20; i++) result[0x10 + i] = key[0x08 + i];
                for (i = 0; i < 0x10; i++) result[0x30 + i] = key[0x28 + i];
            }
        }

        if (dev.AuthMode>2) {
            byte[] blkCFBECB = encodeBlockCipherL3(dev).clone();

            dev.CRCauth4 = MSTbleCalcCRC16((dev.CRCauth2&0xff) + ((dev.CRCauth3&0xff)<<8), result,result.length - 8, 8);
            result[0x06] = IntAsByte(dev.CRCauth4&0xFF);
            result[0x07] = IntAsByte(dev.CRCauth4>>8);

            for (i = 0x10; i < (result.length); i++) {
                result[i] = IntAsByte((ByteAsInt(result[i]) ^ ByteAsInt(blkCFBECB[i & 0x07])));
                blkCFBECB[i & 0x07] = result[i];

                result[i] = IntAsByte(ByteAsInt(result[i]) ^ ByteAsInt(blkCFBECB[8 + (i & 0x07)]));
            }

            for (i = 0x08; i < 0x10; i++) {
                result[i] = IntAsByte(ByteAsInt(result[i]) ^ ByteAsInt(blkCFBECB[i & 0x07]));
                blkCFBECB[i & 0x07] = result[i];

                result[i] = IntAsByte(ByteAsInt(result[i]) ^ ByteAsInt(blkCFBECB[8 + (i & 0x07)]));
            }
        } else {
            for (i = 0; i < 0x04; i++) result[i] = dev.SessionKey[i];
            result[0x04] = IntAsByte(dev.CRCauth1 & 0xFF);
            result[0x05] = IntAsByte(dev.CRCauth1 >> 8);
            result[0x06] = IntAsByte(dev.CRCauth3 & 0xFF);
            result[0x07] = IntAsByte(dev.CRCauth3 >> 8);

            dev.CRCauth4 = MSTbleCalcCRC16(dev.CRCauth2, result, result.length, 0);
            for (i = 0; i < 0x06; i++) result[i] = 0;
            result[0x06] = IntAsByte(dev.CRCauth4 & 0xFF);
            result[0x07] = IntAsByte(dev.CRCauth4 >> 8);
        }

        return result;
    }

    }
