package feature.properties

import android.Manifest
import android.annotation.SuppressLint
import android.app.AlertDialog
import android.app.Service
import android.content.Context
import android.content.Intent
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Build
import android.os.Bundle
import android.os.IBinder
import android.provider.Settings
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.fragment.app.FragmentActivity
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.model.LatLng
import keyless.feature.properties.R

interface LocationChangedListener {
    fun locationChanged(location: Location?)
}
class GpsTracker(context: FragmentActivity) : Service(), LocationListener {

    var requestMultiplePermissions: RequestMultiplePermissionsFragment = RequestMultiplePermissionsFragment(
        context,
        arrayOf(
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.ACCESS_FINE_LOCATION
        ),
        { ondenied ->
        },
        { onShowRationale ->
        }
    )

    private val mContext: Context
    private val MIN_TIME_BW_UPDATES = 0L
    val TAG = "//"
    var alert: AlertDialog? = null

    // flag for GPS status
    var isGPSEnabled = false

    // flag for network status
    var isNetworkEnabled = false

    // flag for GPS status
    var canGetLocation = false
    var mlocation: Location? = null
    var mlatitude = 0.0
    var mlongitude = 0.0

    // Declaring a Location Manager
    private var locationManager: LocationManager? = null
    lateinit var changedListener: LocationChangedListener

    companion object {
//        // The minimum distance to change Updates in meters
// //        private const val MIN_DISTANCE_CHANGE_FOR_UPDATES: Long = 10 // 10 meters
//        private const val MIN_DISTANCE_CHANGE_FOR_UPDATES: Long = 10 // 10 meters
//
// //        // The minimum time between updates in milliseconds
// //        private const val MIN_TIME_BW_UPDATES = (1000 * 60 * 1 // 1 minute
// //                ).toLong()
//        // The minimum time between updates in milliseconds
//        private const val MIN_TIME_BW_UPDATES = (4_000 // 1 minute
//                ).toLong()
    }

    fun requestPermission() {
        requestMultiplePermissions.runWithPermission {
            getLocation()
        }
    }

    init {
        mContext = context
        changedListener = context as LocationChangedListener
    }
    fun setLocationCallback(myInt: LocationChangedListener) {
        changedListener = myInt
    }

    @SuppressLint("MissingPermission")
    fun toCurrentLocation(
        mMap: GoogleMap,
        location: Location?,
        liveLocation: Boolean
    ) {
        val latLng = LatLng(location!!.latitude, location.longitude)
        val cameraUpdate = CameraUpdateFactory.newLatLngZoom(latLng, 13f)
        mMap.animateCamera(cameraUpdate)
        mMap.isMyLocationEnabled = liveLocation
        mMap.uiSettings.isMyLocationButtonEnabled = false
        mMap.uiSettings.isCompassEnabled = false
    }

    @SuppressLint("MissingPermission")
    fun getLocation(): Location? {
        try {
            locationManager = mContext
                .getSystemService(LOCATION_SERVICE) as LocationManager
            // getting GPS status
            isGPSEnabled = locationManager!!
                .isProviderEnabled(LocationManager.GPS_PROVIDER)
            // getting network status
            isNetworkEnabled = locationManager!!
                .isProviderEnabled(LocationManager.NETWORK_PROVIDER)
            if (!isGPSEnabled && !isNetworkEnabled) {
                // no network provider is enabled
            } else {
                canGetLocation = true
                // First get location from Network Provider
                if (isNetworkEnabled) {
                    locationManager!!.requestLocationUpdates(
                        LocationManager.NETWORK_PROVIDER,
                        MIN_TIME_BW_UPDATES,
                        MIN_TIME_BW_UPDATES.toFloat(),
                        this
                    )
                    locationManager!!.requestLocationUpdates(
                        LocationManager.GPS_PROVIDER,
                        MIN_TIME_BW_UPDATES,
                        MIN_TIME_BW_UPDATES.toFloat(),
                        this
                    )
                    if (locationManager != null) {
                        mlocation = locationManager!!
                            .getLastKnownLocation(LocationManager.NETWORK_PROVIDER)
                        if (mlocation != null) {
                            mlatitude = mlocation!!.latitude
                            mlongitude = mlocation!!.longitude
                        }
                    }
                }
                // if GPS Enabled get lat/long using GPS Services
                else if (isGPSEnabled) {
                    if (mlocation == null) {
                        locationManager!!.requestLocationUpdates(
                            LocationManager.GPS_PROVIDER,
                            MIN_TIME_BW_UPDATES,
                            MIN_TIME_BW_UPDATES.toFloat(),
                            this
                        )
                        locationManager!!.requestLocationUpdates(
                            LocationManager.NETWORK_PROVIDER,
                            MIN_TIME_BW_UPDATES,
                            MIN_TIME_BW_UPDATES.toFloat(),
                            this
                        )
                        if (locationManager != null) {
                            mlocation = locationManager!!
                                .getLastKnownLocation(LocationManager.GPS_PROVIDER)
                            if (mlocation != null) {
                                mlatitude = mlocation!!.latitude
                                mlongitude = mlocation!!.longitude
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
        }
        return mlocation
    }

    /**
     * Stop using GPS listener Calling this function will stop using GPS in your
     * app
     */
    @SuppressLint("MissingPermission")
    fun stopUsingGPS() {
        if (locationManager != null) {
            locationManager!!.removeUpdates(this@GpsTracker)
        } else {
        }
    }

    /**
     * Function to get latitude
     */
    fun getLatitude(): Double {
        if (mlocation != null) {
            mlatitude = mlocation!!.latitude
        }

        // return latitude
        return mlatitude
    }

    /**
     * Function to get longitude
     */
    fun getLongitude(): Double {
        if (mlocation != null) {
            mlongitude = mlocation!!.longitude
        }

        // return longitude
        return mlongitude
    }

    /**
     * Function to check GPS/wifi enabled
     *
     * @return boolean
     */
    fun canGetLocation(): Boolean {
        return canGetLocation
    }

    /**
     * Function to show settings alert dialog On pressing Settings button will
     * lauch Settings Options
     */
    fun showSettingsAlert() {
        if (alert == null) {
            val builder = AlertDialog.Builder(mContext)
            // Setting Dialog Title
            builder.setTitle(getString(keyless.data.utils.android.R.string.turn_on_gps_in_settings))

            // Setting Dialog Message
            builder
                .setMessage(getString(keyless.data.utils.android.R.string.gps_is_not_enabled))

            // On pressing Settings button
            builder.setPositiveButton(
                getString(keyless.data.utils.android.R.string.text_settings)
            ) { dialog, which ->
                alert = null
                val intent = Intent(
                    Settings.ACTION_LOCATION_SOURCE_SETTINGS
                )
                mContext.startActivity(intent)
            }

            // on pressing cancel button
            builder.setNegativeButton(
                getString(keyless.feature.common.R.string.text_cancel)
            ) { dialog, which ->
                alert = null
                dialog.cancel()
            }
            alert = builder.create()
            if (!alert!!.isShowing) alert!!.show()
        }
    }

    override fun onLocationChanged(location: Location) {
        changedListener.locationChanged(location)
        stopUsingGPS()
    }

    override fun onProviderDisabled(provider: String) {
        showSettingsAlert()
    }

    override fun onProviderEnabled(provider: String) {
        if (alert != null && alert!!.isShowing) {
            alert!!.dismiss()
            alert = null
        }
    }

    override fun onStatusChanged(provider: String, status: Int, extras: Bundle) {}
    override fun onBind(arg0: Intent): IBinder? {
        return null
    }

    override fun onDestroy() {
        super.onDestroy()
        stopUsingGPS()
    }
}
class RequestMultiplePermissionsFragment(
    activity: FragmentActivity,
    private val permission: Array<String>,
    onDenied: (Map<String, Boolean>) -> Unit = {},
    onShowRationale: (Map<String, Boolean>) -> Unit = {}
) {
    private var onGranted: () -> Unit = {}

    @RequiresApi(Build.VERSION_CODES.M)
    private val launcher =
        activity.registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { isGranted ->
            val pending = isGranted.filterValues { values -> !values }
            for (i in pending.keys) {
                if (activity.shouldShowRequestPermissionRationale(i)) {
                    onShowRationale(pending)
                    return@registerForActivityResult
                }
            }
            when {
                pending.isNullOrEmpty() -> onGranted()
                else -> onDenied(pending)
            }
        }

    @SuppressLint("NewApi")
    fun runWithPermission(onGranted: () -> Unit) {
        this.onGranted = onGranted
        launcher.launch(permission)
    }
}