package feature.properties

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View.GONE
import android.view.View.VISIBLE
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.LatLng
import data.common.preferences.Preferences
import data.common.preferences.Roles
import data.network.android.ApiUtils
import data.network.android.models.Property
import data.utils.android.CommonValues
import data.utils.android.CommonValues.Companion.directionStockMap
import data.utils.android.settings.SharedPreferenceUtils
import keyless.feature.properties.databinding.ActivityPropertyDetailBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class PropertyDetailActivity : AppCompatActivity(), OnMapReadyCallback {
    private var model: Property? = null
    lateinit var map: GoogleMap
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    private lateinit var binding: ActivityPropertyDetailBinding

    private val launcher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (it.resultCode == RESULT_OK) {
            map.clear()
            model = it.data?.getParcelableExtra("model")
            setData()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPropertyDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)
        getDataIntent()
        onClicks()
        loadMap()
    }

    override fun onMapReady(p0: GoogleMap) {
        map = p0
        setData()
        map.setOnMapClickListener {
            directionStockMap(this, model?.latitude, model?.longitude)
        }

        map.setOnMarkerClickListener { marker ->
            directionStockMap(this, model?.latitude, model?.longitude)
            false
        }
    }

    private fun getDataIntent() {
        model = if (Build.VERSION.SDK_INT >= 33) {
            intent.getParcelableExtra("model", Property::class.java)
        } else {
            intent?.getParcelableExtra("model")
        }

        if (Preferences.role.get() == Roles.CUSTOMER_SERVICES || Preferences.role.get() == Roles.VIEWER_ACCESS) {
            binding.viewEditProperty.isVisible = false
            binding.imageView4.isVisible = false
        } else {
            binding.viewEditProperty.isVisible = true
            binding.imageView4.isVisible = true
        }
    }

    @SuppressLint("SetTextI18n")
    private fun setData() {
        model?.let { it ->
            if (true == it.icon?.isNotEmpty()) {
                it.icon?.first()?.let {
                    Glide.with(this)
                        .load(ApiUtils.IMAGE_BASE_URL + it.icon)
                        .placeholder(keyless.feature.common.R.drawable.iv_other_icon)
                        .into(binding.placeHolder)
                }
            }
            binding.propertyNameTV.text = it.building_name
            binding.locAddressTV.text = it.area + ", " + it.emirate
            binding.floorTV.text = it.total_floors.toString()
            binding.lockTV.text = it.total_locks

            // Maintain
            val grocery_number = it.grocery_number
            if (grocery_number.isNullOrEmpty()) {
                binding.textView23.visibility = GONE
                binding.maintainTV.visibility = GONE
            } else {
                binding.textView23.isVisible = true
                binding.maintainTV.isVisible = true
                binding.maintainTV.text = grocery_number
            }

            // Laundry
            val laundary_number = it.laundary_number
            if (laundary_number.isNullOrEmpty()) {
                binding.textView24.visibility = GONE
                binding.laundryTV.visibility = GONE
            } else {
                binding.textView24.isVisible = true
                binding.laundryTV.isVisible = true
                binding.laundryTV.text = laundary_number
            }

            // Support
            val supportCallNumber = it.support_call_number
            if (supportCallNumber.isNullOrEmpty()) {
                binding.textView26.visibility = GONE
                binding.supportTV.visibility = GONE
            } else {
                binding.textView26.isVisible = true
                binding.supportTV.visibility = VISIBLE
                binding.supportTV.text = supportCallNumber
            }

            // whatsapp
            val support_whatsapp_number = it.support_whatsapp_number
            if (support_whatsapp_number.isNullOrEmpty()) {
                binding.textView27.visibility = GONE
                binding.whatsappTV.visibility = GONE
            } else {
                binding.textView27.isVisible = true
                binding.whatsappTV.visibility = VISIBLE
                binding.whatsappTV.text = support_whatsapp_number
            }

            it.latitude?.toDoubleOrNull()?.let { it1 ->
                lifecycleScope.launch {
                    delay(800)
                    val latLng = LatLng(it1, it.longitude!!.toDouble())
                    val marker = map.addMarker(
                        CommonValues.addCutomMarker(this@PropertyDetailActivity, latLng, it.icon)
                    )
                    marker?.tag = it._id
                    map.animateCamera(CameraUpdateFactory.newLatLngZoom(latLng, 15f))
                }
            }
        }
    }

    private fun onClicks() {
        binding.backBtnProperty.setOnClickListener { finish() }
        binding.viewEditProperty.setOnClickListener {
            launcher.launch(Intent(this, feature.properties.AddProperty::class.java).putExtra("model", model))
        }
    }

    private fun loadMap() {
        val fm = supportFragmentManager
        val supportMapFragment = SupportMapFragment.newInstance()
        fm.beginTransaction().replace(binding.propertyDetailMap.id, supportMapFragment).commit()
        supportMapFragment.getMapAsync(this)
    }
}