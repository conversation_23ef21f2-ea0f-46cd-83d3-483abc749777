<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    tools:context="feature.properties.SearchLocationActivity">

    <ImageView
        android:id="@+id/backBtnProperty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:padding="20dp"
        style="@style/ImageMirror"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/select_location"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtnProperty"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtnProperty" />

    <FrameLayout
        android:background="@drawable/white_top_corners"
        android:layout_width="0dp"
        android:layout_marginTop="10dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtnProperty" >
        <FrameLayout
            android:id="@+id/searchMapContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
        <ImageView
            android:layout_gravity="center"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:src="@drawable/location_pin"/>
        <AutoCompleteTextView
            android:elevation="2dp"
            android:drawablePadding="10dp"
            android:id="@+id/searchLocET"
            android:padding="12dp"
            style="@style/mirrorText"
            android:imeOptions="actionSearch"
            android:singleLine="true"
            android:layout_margin="20dp"
            android:drawableStart="@drawable/search"
            android:background="@drawable/bg_btn_round"
            android:backgroundTint="@color/white"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_gravity="bottom"
            android:background="@color/white"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_marginStart="20dp"
                android:id="@+id/imageView8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:src="@drawable/marker"/>

            <TextView
                android:layout_marginEnd="20dp"
                android:layout_marginTop="10dp"
                android:id="@+id/locAddress"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:textSize="16dp"
                android:fontFamily="@font/poppins_medium_500"
                android:text="@string/fetching_location"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/imageView8"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                style="@style/buttonStyle"
                android:layout_marginBottom="30dp"
                android:id="@+id/saveBtnAdd"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginTop="10dp"
                android:text="@string/btnSave"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/locAddress"
                app:layout_constraintWidth_percent="0.7" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>