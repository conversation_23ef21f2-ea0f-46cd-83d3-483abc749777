<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    android:layout_height="match_parent">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/colorAccent"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        tools:context=".dashboard.properties.detail.PropertyDetailActivity">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/backBtnProperty"
                style="@style/ImageMirror"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:padding="20dp"
                android:src="@drawable/iv_back_white"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/propertyEditBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:text="@string/property_location"
                android:textColor="@color/white"
                android:textSize="18dp"
                app:layout_constraintBottom_toBottomOf="@+id/backBtnProperty"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/backBtnProperty" />


            <ImageView
                android:id="@+id/imageView4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:src="@drawable/ic_edit"
                app:layout_constraintBottom_toBottomOf="@+id/propertyEditBtn"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/propertyEditBtn" />

            <View
                android:id="@+id/viewEditProperty"
                android:layout_width="60dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_marginTop="8dp"
            android:layout_height="match_parent"
            android:background="@drawable/white_top_corners"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/backBtnProperty">


            <FrameLayout
                android:id="@+id/iconDetailLayout"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginTop="25dp"
                android:background="@drawable/bg_button_rounded"
                android:backgroundTint="@color/white"
                android:elevation="5dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/placeHolder"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:padding="22dp"
                    android:src="@drawable/iv_other_icon" />


            </FrameLayout>

            <TextView
                android:id="@+id/propertyNameTV"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:gravity="center"
                android:includeFontPadding="false"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iconDetailLayout" />

            <ImageView
                android:id="@+id/imageView8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="4dp"
                android:src="@drawable/marker"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/locAddressTV" />

            <TextView
                android:id="@+id/locAddressTV"
                style="@style/mirrorText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="16dp"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/fetching_location"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/imageView8"
                app:layout_constraintTop_toBottomOf="@+id/propertyNameTV" />

            <TextView
                android:id="@+id/textView21"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/no_of_floor"
                android:textColor="@color/black"
                app:layout_constraintStart_toStartOf="@+id/imageView8"
                app:layout_constraintTop_toBottomOf="@id/locAddressTV" />

            <TextView
                android:id="@+id/floorTV"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:textColor="@color/black"
                app:layout_constraintStart_toEndOf="@+id/textView21"
                app:layout_constraintTop_toTopOf="@+id/textView21" />

            <TextView
                android:id="@+id/textView22"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/locks"
                android:textColor="@color/black"
                app:layout_constraintStart_toStartOf="@+id/textView21"
                app:layout_constraintTop_toBottomOf="@id/textView21" />

            <TextView
                android:id="@+id/lockTV"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:textColor="@color/black"
                app:layout_constraintStart_toEndOf="@+id/textView22"
                app:layout_constraintTop_toTopOf="@+id/textView22" />

            <TextView
                android:id="@+id/textView23"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/maintenance_number_dot"
                android:textColor="@color/black"
                app:layout_constraintStart_toStartOf="@+id/textView22"
                app:layout_constraintTop_toBottomOf="@id/textView22" />

            <TextView
                android:id="@+id/maintainTV"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:textColor="@color/black"
                app:layout_constraintStart_toEndOf="@+id/textView23"
                app:layout_constraintTop_toTopOf="@+id/textView23" />

            <TextView
                android:id="@+id/textView24"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/laundry_number_dot"
                android:textColor="@color/black"
                app:layout_constraintStart_toStartOf="@+id/textView22"
                app:layout_constraintTop_toBottomOf="@id/textView23" />

            <TextView
                android:id="@+id/laundryTV"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:textColor="@color/black"
                app:layout_constraintStart_toEndOf="@+id/textView24"
                app:layout_constraintTop_toTopOf="@+id/textView24" />

            <TextView
                android:id="@+id/textView26"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/call"
                android:textColor="@color/black"
                app:layout_constraintStart_toStartOf="@+id/textView22"
                app:layout_constraintTop_toBottomOf="@+id/textView24" />

            <TextView
                android:id="@+id/supportTV"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:textColor="@color/black"
                app:layout_constraintStart_toEndOf="@+id/textView26"
                app:layout_constraintTop_toTopOf="@+id/textView26" />

            <TextView
                android:id="@+id/textView27"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/whatsapp"
                android:textColor="@color/black"
                app:layout_constraintStart_toStartOf="@+id/textView22"
                app:layout_constraintTop_toBottomOf="@+id/textView26" />

            <TextView
                android:id="@+id/whatsappTV"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:textColor="@color/black"
                app:layout_constraintStart_toEndOf="@+id/textView27"
                app:layout_constraintTop_toTopOf="@+id/textView27" />

            <TextView
                android:id="@+id/textView25"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/location"
                android:textColor="@color/black"
                android:textSize="18dp"
                app:layout_constraintStart_toStartOf="@+id/textView24"
                app:layout_constraintTop_toBottomOf="@+id/textView27" />

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="167dp"
                android:layout_marginEnd="20dp"
                android:elevation="4dp"
                app:cardCornerRadius="25dp"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView25"
                app:layout_constraintTop_toBottomOf="@+id/textView25">

                <FrameLayout
                    android:id="@+id/propertyDetailMap"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </androidx.cardview.widget.CardView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
