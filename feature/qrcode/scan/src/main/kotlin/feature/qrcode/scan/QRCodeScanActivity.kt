package feature.qrcode.scan

import android.Manifest
import android.content.ContentValues
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.google.zxing.Result
import com.karumi.dexter.Dexter
import com.karumi.dexter.PermissionToken
import com.karumi.dexter.listener.PermissionDeniedResponse
import com.karumi.dexter.listener.PermissionGrantedResponse
import com.karumi.dexter.listener.PermissionRequest
import com.karumi.dexter.listener.single.PermissionListener
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import keyless.data.utils.android.R
import keyless.feature.qrcode.scan.databinding.ActivityAddLockScannerBinding
import me.dm7.barcodescanner.zxing.ZXingScannerView

class QRCodeScanActivity : AppCompatActivity(), ZXingScannerView.ResultHandler {

    private lateinit var binding: ActivityAddLockScannerBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddLockScannerBinding.inflate(layoutInflater)
        setContentView(binding.root)
        checkCameraPermission()
    }

    private fun checkCameraPermission() {
        Dexter.withContext(this).withPermission(Manifest.permission.CAMERA)
            .withListener(object : PermissionListener, ZXingScannerView.ResultHandler {
                override fun onPermissionGranted(p0: PermissionGrantedResponse?) {
                    binding.scanner?.setResultHandler(this)
                    binding.scanner?.startCamera()
                }

                override fun onPermissionDenied(p0: PermissionDeniedResponse?) {
                    askPermission()
                }

                override fun onPermissionRationaleShouldBeShown(
                    p0: PermissionRequest?,
                    p1: PermissionToken?
                ) {
                    askPermission()
                }

                override fun handleResult(p0: Result?) {
                    p0?.let { Log.v(ContentValues.TAG, it.text) }
                    Log.v(ContentValues.TAG, p0?.barcodeFormat.toString())
                    binding.scanner?.resumeCameraPreview(this)
                }
            }).check()
    }

    private fun askPermission() {
        requestPermissions(
            arrayOf(
                Manifest.permission.CAMERA
            ),
            50
        )
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (
            grantResults.isNotEmpty() &&
            grantResults[0] == PackageManager.PERMISSION_GRANTED
        ) {
            binding.scanner?.setResultHandler(this)
            binding.scanner?.startCamera()
        } else {
            showDialogForPermissions()
        }
    }

    private fun showDialogForPermissions() {
        defaultDialog(
            this,
            getString(R.string.please_allow_permissions_to_continue),
            object : OnActionOK {
                override fun onClickData() {
                    startActivityForResult(
                        Intent(
                            Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                            Uri.fromParts("package", packageName, null)
                        ),
                        10
                    )
                }
            }
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10) {
            askPermission()
        }
    }

    override fun handleResult(p0: Result?) {
        binding.scanner.stopCamera()
        val res = p0.toString()
        val resultCallback = intent.getIntExtra(QR_CODE_SCAN_RESULT_CODE, 24)
        intent.putExtra(QR_CODE_SCAN_RESULT_DATA, res)
        setResult(resultCallback, intent)
        finish()
    }

    override fun onResume() {
        super.onResume()
        binding.scanner?.setResultHandler(this)
    }

    override fun onPause() {
        super.onPause()
        binding.scanner?.stopCamera()
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.scanner?.stopCamera()
    }

    companion object {
        const val QR_CODE_SCAN_RESULT_CODE = "qr_code_scan_result_code"
        const val QR_CODE_SCAN_RESULT_DATA = "qr_code_scan_result_data"
    }
}