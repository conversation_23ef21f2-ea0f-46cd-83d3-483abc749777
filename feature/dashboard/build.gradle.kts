plugins {
    id("keyless.android.feature")
    id("keyless.android.compose")
    id("org.jetbrains.kotlin.kapt")
}

android {
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":core-permissions-manager"))
    implementation(project(":core-location-google"))
    implementation(project(":core-lock-airbnk"))
    implementation(project(":core-lock-tedee"))
    implementation(project(":core-lock-ttlock"))
    implementation(project(":data-network-android"))
    implementation(project(":data-utils-android"))
    implementation(project(":data-common"))
    implementation(project(":domain-common"))
    implementation(project(":feature-common"))
    implementation(project(":feature-routines"))
    implementation(project(":feature-settings"))
    implementation(project(":feature-settings-checkin"))

    implementation(project(":core-lock-iseo"))
    implementation(project(":rayonicsSdk"))
    // implementation(project(":ttlockSdk"))
    implementation(project(":core-lock-mst"))

    implementation(keyless.androidx.compose.activity)
    implementation(keyless.androidx.lifecycle.livedata)
    implementation(keyless.kotlinx.datetime)
    implementation(keyless.retrofit)
    implementation(keyless.glide)
    kapt(keyless.glide.compiler)

    implementation("com.mikhaellopez:circularimageview:4.3.1")
    implementation("org.greenrobot:eventbus:3.3.1")
    implementation ("com.github.mukeshsolanki:android-otpview-pinview:3.2.0")
    implementation ("com.github.khoyron:Actionsheet-android:4")
    implementation("com.google.android.gms:play-services-maps:18.1.0")
    implementation("com.google.android.gms:play-services-location:21.0.1")
    implementation("com.skyfishjy.ripplebackground:library:1.0.1")
    implementation("com.alibaba:fastjson:1.1.60.android")
    implementation("com.github.codersrouteandroid:flexible-switch:1.0")
    implementation("com.wdullaer:materialdatetimepicker:4.2.3")

}