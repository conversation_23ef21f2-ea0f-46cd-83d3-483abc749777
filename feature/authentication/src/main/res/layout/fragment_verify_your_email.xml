<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="feature.authentication.signup.VerifyYourEmailFragment">

    <TextView
        android:id="@+id/textView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:text="@string/verify_your_email"
        android:textColor="@color/black"
        android:textSize="22dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView5"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/enter_the_email_which_you_want_to_associate_with_your_accountand_we_ll_send_an_email_with_verification_link"
        android:textColor="@color/black"
        android:textSize="16dp"
        android:fontFamily="@font/poppins_medium_500"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView" />


    <ImageView
        android:id="@+id/imageView2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:src="@drawable/iv_verify_email"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView5" />


    <EditText
        android:id="@+id/tvEmail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_edit_corners"
        android:fontFamily="@font/poppins_medium_500"
        android:hint="@string/enter_email"
        android:inputType="text"
        android:padding="10dp"
        style="@style/mirrorText"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/textView9"
        app:layout_constraintTop_toBottomOf="@+id/textView9" />

    <TextView
        android:id="@+id/textView9"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/poppins_regular_400"
        android:text="@string/email"
        android:textColor="@color/black"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView2" />


    <TextView
        android:id="@+id/signUpBtn"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="60dp"
        android:layout_marginEnd="60dp"
        android:layout_marginBottom="54dp"
        android:background="@drawable/bg_btn_round"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/verify_email"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>