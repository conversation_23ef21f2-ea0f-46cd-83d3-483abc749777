<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context="feature.authentication.login.ForgotPasswordFragment">


    <TextView
        android:id="@+id/textViewHeading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:fontFamily="@font/poppins_bold_700"
        android:text="@string/forgot_password"
        android:textColor="@color/black"
        android:textSize="22dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/textView9"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_regular_400"
        android:text="@string/email"
        android:layout_marginTop="30dp"
        android:textColor="@color/black"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewHeading" />


    <EditText
        android:id="@+id/tvEmail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_edit_corners"
        android:fontFamily="@font/poppins_medium_500"
        android:hint="@string/enter_email"
        style="@style/mirrorText"
        android:inputType="text"
        android:padding="10dp"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/textView9"
        app:layout_constraintTop_toBottomOf="@+id/textView9" />

    <TextView
        android:id="@+id/btnNext"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="60dp"
        android:layout_marginEnd="60dp"
        android:layout_marginBottom="54dp"
        android:background="@drawable/bg_btn_round"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/submit"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />



</androidx.constraintlayout.widget.ConstraintLayout>