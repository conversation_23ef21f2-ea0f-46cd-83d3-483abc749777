<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    android:id="@+id/parentSignupLayout"
    tools:context="feature.authentication.login.LoginMainActivity">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        style="@style/ImageMirror"
        android:src="@drawable/keyless_logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        style="@style/ImageMirror"
        android:layout_marginTop="30dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />

    <View
        android:layout_width="30dp"
        android:id="@+id/backButton"
        android:layout_height="30dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="30dp"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imageView" />


    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="30dp"
        android:id="@+id/frameContainer"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:visibility="visible"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView"
        app:layout_constraintVertical_bias="0.0" />


    <LinearLayout
        android:id="@+id/linearWeb"
        android:layout_width="match_parent"
        android:layout_marginTop="30dp"
        android:paddingTop="30dp"
        android:background="@drawable/white_top_corners"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView"
        android:layout_height="0dp">


        <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintVertical_bias="0.0" />

    </LinearLayout>






</androidx.constraintlayout.widget.ConstraintLayout>