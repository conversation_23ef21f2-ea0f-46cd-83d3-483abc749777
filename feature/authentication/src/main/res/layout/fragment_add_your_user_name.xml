<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/textView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/create_guest"
            android:textColor="@color/black"
            android:textSize="22dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/titlePage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="34dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/firstname"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView" />


        <EditText
            android:id="@+id/tvFirstName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/bg_edit_corners"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/firstname"
            android:inputType="textCapWords"
            style="@style/mirrorText"
            android:padding="14dp"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/titlePage" />


        <TextView
            android:id="@+id/textView11"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/last_name"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvFirstName" />


        <EditText
            android:id="@+id/tvLastName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/bg_edit_corners"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/last_name"
            android:inputType="textCapWords"
            android:padding="14dp"
            style="@style/mirrorText"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView11" />


        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/user_name"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvLastName" />

        <EditText
            android:id="@+id/tvUserName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/bg_edit_corners"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/user_name"
            android:inputType="text"
            style="@style/mirrorText"
            android:padding="14dp"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView2" />


        <TextView
            android:id="@+id/textView3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/password"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvUserName" />

        <EditText
            android:id="@+id/tvPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/bg_edit_corners"
            android:ems="6"
            android:fontFamily="@font/poppins_medium_500"
            style="@style/mirrorText"
            android:hint="@string/password"
            android:inputType="textPassword"
            android:padding="14dp"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView3" />


        <ImageView
            android:id="@+id/showPassBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_eye_hide"
            app:layout_constraintBottom_toBottomOf="@+id/tvPassword"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvPassword" />

        <CheckBox
            android:id="@+id/check1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:checked="false"
            android:paddingStart="6dp"
            android:includeFontPadding="false"
            android:focusable="false"
            android:clickable="false"
            android:textSize="13dp"
            android:button="@drawable/checkbox"
            android:text="@string/at_least_8_characters"
            android:fontFamily="@font/poppins_regular_400"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvPassword" />

        <CheckBox
            android:id="@+id/check2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:checked="false"
            android:paddingStart="6dp"
            android:includeFontPadding="false"
            android:textSize="13dp"
            android:button="@drawable/checkbox"
            android:focusable="false"
            android:clickable="false"
            android:fontFamily="@font/poppins_regular_400"
            android:text="@string/containing_a_letter"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/check1" />

        <CheckBox
            android:id="@+id/check3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:checked="false"
            android:paddingStart="6dp"
            android:includeFontPadding="false"
            android:textSize="13dp"
            android:focusable="false"
            android:clickable="false"
            android:button="@drawable/checkbox"
            android:text="@string/a_number"
            android:fontFamily="@font/poppins_regular_400"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/check2" />



        <TextView
            android:id="@+id/textView4"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:ems="6"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/confirm_password"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/check3" />

        <EditText
            android:id="@+id/tvConfirmPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/bg_edit_corners"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/confirm_password"
            android:inputType="textPassword"
            android:padding="14dp"
            android:textColor="@color/black"
            style="@style/mirrorText"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView4" />


        <ImageView
            android:id="@+id/showPassConfirmBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_eye_hide"
            app:layout_constraintBottom_toBottomOf="@+id/tvConfirmPassword"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvConfirmPassword" />

        <TextView
            android:id="@+id/termsTxt"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="13dp"
            android:fontFamily="@font/poppins_medium_500"
            android:layout_marginBottom="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/by_signing_up_you_agree_to_the_terms_and_conditions"
            app:layout_constraintBottom_toTopOf="@+id/btnSignUp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/btnSignUp"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="60dp"
            android:layout_marginTop="90dp"
            android:layout_marginEnd="60dp"
            android:layout_marginBottom="40dp"
            android:background="@drawable/bg_btn_round"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/sign_up"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvConfirmPassword"
            app:layout_constraintVertical_bias="0.0" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>


