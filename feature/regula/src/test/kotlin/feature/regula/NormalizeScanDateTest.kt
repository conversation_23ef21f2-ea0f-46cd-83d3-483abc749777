package feature.regula

import kotlin.test.Test
import kotlin.test.assertEquals

class NormalizeScanDateTest {

    @Test
    fun `test normalize date`() {
        assertEquals("2021-08-13", normalizeDate("2021-08-13"))
        assertEquals("2021-08-13", normalizeDate("2021-13-08"))
        assertEquals("2021-08-13", normalizeDate("08-13-2021"))
        assertEquals("2021-08-13", normalizeDate("2021/13/08"))
        assertEquals("2021-08-13", normalizeDate("2021/13/8"))
        assertEquals("2021-08-13", normalizeDate("08/13/2021"))
        assertEquals("2021-08-13", normalizeDate("08/13/21"))
        assertEquals("2021-08-13", normalizeDate("13/08/21"))

        assertEquals("2021-08-13", normalizeDate("١٣/٠٨/٢١"))
        assertEquals("2021-08-13", normalizeDate("١٣/٠٨/٢٠٢١"))
        assertEquals("2021-08-13", normalizeDate("٢٠٢١/٠٨/١٣"))

        assertEquals("2021-01-01", normalizeDate("2021-01-01"))
        assertEquals("2021-01-01", normalizeDate("2021-1-1"))
        assertEquals("2021-01-01", normalizeDate("2021/1/1"))
        assertEquals("2021-01-01", normalizeDate("2021.1.1"))

        assertEquals("2021-01-01", normalizeDate("01-01-2021"))
        assertEquals("2021-01-01", normalizeDate("1-1-2021"))
        assertEquals("2021-01-01", normalizeDate("1/1/2021"))
        assertEquals("2021-01-01", normalizeDate("1.1.2021"))
    }
}