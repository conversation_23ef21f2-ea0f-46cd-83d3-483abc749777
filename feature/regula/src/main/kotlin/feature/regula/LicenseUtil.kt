package feature.regula

import android.content.Context
import keyless.feature.regula.R
import java.io.IOException

object LicenseUtil {
//    fun readFileFromAssets(
//        assetPackageName: String,
//        fileName: String,
//        context: Context
//    ): ByteArray? {
//        val licInput: InputStream
//        try {
//            if (context.assets.list(assetPackageName) == null
//                || !Arrays.asList(*context.assets.list(assetPackageName)).contains(fileName)
//            ) {
//                Log.e("FileUtil", "asset: $fileName is absent")
//                return null
//            }
//        } catch (e: IOException) {
//            e.printStackTrace()
//        }
//        try {
//            licInput = context.assets.open("$assetPackageName/$fileName")
//            val available = licInput.available()
//            if (available == 0) return null
//            val license = ByteArray(available)
//            licInput.read(license)
//            licInput.close()
//            return license
//        } catch (e: Exception) {
//            e.message
//        }
//        return null
//    }
    fun readFileFromAssets(context: Context?): ByteArray? {
        if (context == null) return null
        val licInput = context.resources.openRawResource(R.raw.regula)
        val available: Int = try {
            licInput.available()
        } catch (e: IOException) {
            return null
        }
        val license = ByteArray(available)
        try {
            licInput.read(license)
        } catch (e: IOException) {
            return null
        }
        return license
    }
}