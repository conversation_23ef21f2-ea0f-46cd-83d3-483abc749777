package feature.regula.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class DocModel(

    @SerializedName("OneCandidate") var OneCandidate: OneCandidate? = OneCandidate(),
    @SerializedName("Status") var status: DocStatus? = null,
    @SerializedName("buf_length") var bufLength: Int? = null,
    @SerializedName("light") var light: Int? = null,
    @SerializedName("list_idx") var listIdx: Int? = null,
    @SerializedName("page_idx") var pageIdx: Int? = null,
    @SerializedName("result_type") var resultType: Int? = null,
    @SerializedName("DocGraphicsInfo") var docGraphics: DocGraphicsInfo? = null

)

@Keep
data class DocStatus(var detailsOptical: DetailsOpticalDoc? = null)

@Keep
data class DetailsOpticalDoc(var expiry: Int)

// images
@Keep
data class DocGraphicsInfo(var nFields: Int, var pArrayFields: List<PArrayFields>? = null)

@Keep
data class PArrayFields(@SerializedName("FieldName") var fieldName: String, var image: ImageModel? = null)

@Keep
data class ImageModel(var format: String, var image: String)