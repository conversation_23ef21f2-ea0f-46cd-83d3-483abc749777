<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context="feature.regula.DocScanActivity">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/colorAccent"
        app:layout_constraintBottom_toTopOf="@+id/guideline7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:context=".scanner.DocScanActivity" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guideline7"
        tools:context=".scanner.DocScanActivity" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            style="@style/ImageMirror"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:src="@drawable/iv_back_white"
            app:layout_constraintBottom_toBottomOf="@+id/imageView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/imageView" />

        <View
            android:id="@+id/viewBack"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginStart="16dp"
            app:layout_constraintBottom_toBottomOf="@+id/imageView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/imageView" />


        <TextView
            android:id="@+id/imageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:fontFamily="@font/poppins_medium_500"
            android:text="@string/upload_documents"
            android:textColor="@color/white"
            android:textSize="18dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/white_top_corners"
            app:layout_constraintBottom_toTopOf="@+id/button_frame"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageView"
            app:layout_constraintVertical_bias="0.0">


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:paddingVertical="20dp"
                android:visibility="visible">

                <TextView
                    android:id="@+id/select_doc_type_txt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:fontFamily="@font/poppins_bold_700"
                    android:text="@string/select_document_type"
                    android:textColor="@color/black"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />




                <EditText
                    android:id="@+id/select_doc_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/bg_edit_corners"
                    android:clickable="true"
                    android:drawableEnd="@drawable/ic_arrow_downward"
                    android:editable="false"
                    android:focusable="false"
                    style="@style/mirrorText"
                    android:fontFamily="@font/poppins_medium_500"
                    android:hint="@string/select_document_type"
                    android:includeFontPadding="false"
                    android:minWidth="400dp"
                    android:padding="14dp"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toEndOf="@+id/select_doc_type_txt"
                    app:layout_constraintStart_toStartOf="@+id/select_doc_type_txt"
                    app:layout_constraintTop_toBottomOf="@id/select_doc_type_txt" />

<!--                <EditText-->
<!--                    android:id="@+id/select_doc_type"-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginTop="4dp"-->
<!--                    android:background="@drawable/bg_edit_corners"-->
<!--                    android:clickable="true"-->
<!--                    android:drawableEnd="@drawable/ic_arrow_downward"-->
<!--                    android:editable="false"-->
<!--                    android:focusable="false"-->
<!--                    android:fontFamily="@font/poppins_medium_500"-->
<!--                    android:hint="@string/select_document_type"-->
<!--                    android:includeFontPadding="false"-->
<!--                    android:padding="14dp"-->
<!--                    android:singleLine="true"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="16dp"-->
<!--                    app:layout_constraintEnd_toEndOf="@+id/select_doc_type_txt"-->
<!--                    app:layout_constraintStart_toStartOf="@+id/select_doc_type_txt"-->
<!--                    app:layout_constraintTop_toBottomOf="@id/select_doc_type_txt" />-->

                <TextView
                    android:id="@+id/front_side_txt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="25dp"
                    android:layout_marginEnd="16dp"
                    android:fontFamily="@font/poppins_bold_700"
                    android:text="@string/front_side"
                    android:textColor="@color/black"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/select_doc_type" />


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/mainLayFront"
                    android:layout_width="match_parent"
                    android:layout_height="230dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/empty_image_bg"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/front_side_txt">


                    <ImageView
                        android:id="@+id/imageView13"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/staff_add_img"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.43" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:fontFamily="@font/poppins_semibold_600"
                        android:text="@string/capture_document"
                        android:textColor="@color/black"
                        android:textSize="16dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/imageView13" />


                    <ImageView
                        android:id="@+id/docImageFront"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_margin="16dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                </androidx.constraintlayout.widget.ConstraintLayout>


                <TextView
                    android:id="@+id/back_side_txt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="25dp"
                    android:layout_marginEnd="16dp"
                    android:fontFamily="@font/poppins_bold_700"
                    android:text="@string/back_side"
                    android:textColor="@color/black"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/mainLayFront" />


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/mainLayBack"
                    android:layout_width="match_parent"
                    android:layout_height="230dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/empty_image_bg"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/back_side_txt"
                    app:layout_constraintVertical_bias="0.0">

                    <ImageView
                        android:id="@+id/imageView14"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/staff_add_img"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.43" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:fontFamily="@font/poppins_semibold_600"
                        android:text="@string/capture_document"
                        android:textColor="@color/black"
                        android:textSize="16dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/imageView14" />


                    <ImageView
                        android:id="@+id/docImageBack"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_margin="16dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                </androidx.constraintlayout.widget.ConstraintLayout>


                <TextView
                    android:id="@+id/txtDocument"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="14dp"
                    android:layout_marginEnd="16dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:includeFontPadding="false"
                    android:text="@string/document_status"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/mainLayBack" />

                <TextView
                    android:id="@+id/txtDocumentStatus"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="14dp"
                    android:layout_marginEnd="16dp"
                    android:fontFamily="@font/poppins_regular_400"
                    android:includeFontPadding="false"
                    android:textColor="@color/green"
                    android:textSize="16dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/txtDocument" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <FrameLayout
            android:id="@+id/button_frame"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:elevation="10dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/txtBtnProceed"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginStart="60dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="60dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/bg_btn_round"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/next"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintTop_toBottomOf="@+id/imageView14" />

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:background="#33000000"
        android:id="@+id/progressLay"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        android:layout_height="match_parent">


        <LinearLayout
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:gravity="center"
            android:background="@drawable/white_all_corners_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_gravity="center"
                android:theme="@style/progressBarBlue"
                android:progressTint="@color/colorAccent"
                android:progressBackgroundTint="@color/colorAccent"
                android:layout_height="wrap_content"/>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>




    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline7"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

</androidx.constraintlayout.widget.ConstraintLayout>