package feature.regula.refactored.documents

import android.graphics.Bitmap
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import core.regula.common.models.RegulaImage
import domain.regula.models.Event
import domain.regula.models.ScreenEvent
import domain.regula.models.ScreenState
import feature.common.compose.button.AppButton
import feature.common.compose.dropdown.AppDropDownGroup
import feature.common.compose.icons.AppIcon
import feature.common.compose.images.AppBitmapImage
import feature.common.compose.surface.AppClickableSurface
import feature.common.compose.surface.AppColumn
import feature.common.compose.surface.AppRow
import feature.common.compose.text.AppBodyText
import feature.common.compose.text.AppLabelText
import feature.common.compose.theme.Dimensions
import feature.regula.refactored.documents.models.UIReadyState
import feature.regula.refactored.documents.models.dropDownItem
import keyless.feature.regula.refactored.R

@Composable
internal fun DocumentVerificationReadyScreen(
    modifier: Modifier = Modifier,
    state: UIReadyState,
    onEvent: (Event) -> Unit
) {
    AppColumn(
        modifier = modifier
    ) {
        var expanded by remember { mutableStateOf(false) }
        AppLabelText(text = stringResource(id = R.string.select_document_type))

        AppDropDownGroup(
            hint = stringResource(id = R.string.select_document_type),
            items = state.documentTypes.map { it.dropDownItem() }.filterNotNull(),
            selected = state.selectedDocumentType?.dropDownItem(),
            expanded = expanded,
            onExpand = { expanded = !expanded },
            onDismiss = { expanded = false },
            onItemSelected = {
                onEvent(ScreenEvent.SelectDocumentType(it))
                expanded = false
            }
        )

        if (state.selectedDocumentType != ScreenState.DocumentTypeState.NONE) {
            AppLabelText(text = stringResource(id = R.string.front_side))

            SideSurface(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                name = state.frontSide?.fullName,
                dateOfBirth = state.frontSide?.dateOfBirth,
                image = state.frontSide?.image,
                type = state.frontSide?.typeDescription,
                expiryDate = state.frontSide?.expiryDate,
                page = state.frontSide?.pageNumber ?: 1,
                documentNumber = state.frontSide?.documentNumber,
                enabled = true
            ) {
                onEvent(ScreenEvent.ScanFrontSide)
            }
        }

        if (state.selectedDocumentType == ScreenState.DocumentTypeState.UAE_ID) {
            AppLabelText(text = stringResource(id = R.string.back_side))

            SideSurface(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                name = state.backSide?.fullName,
                dateOfBirth = state.backSide?.dateOfBirth,
                image = state.backSide?.image,
                type = state.backSide?.typeDescription,
                expiryDate = state.backSide?.expiryDate,
                page = state.backSide?.pageNumber ?: 2,
                documentNumber = state.backSide?.documentNumber,
                enabled = state.frontSide != null
            ) {
                onEvent(ScreenEvent.ScanBackSide)
            }
        }

        AppButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = R.string.next),
            enabled = state.isDocumentScanComplete,
            onClick = {
                onEvent(ScreenEvent.DocumentsNextClick)
            }
        )
    }
}

@Composable
private fun SideSurface(
    modifier: Modifier = Modifier,
    name: String?,
    dateOfBirth: String?,
    image: RegulaImage?,
    type: String?,
    expiryDate: String?,
    page: Int,
    documentNumber: String?,
    enabled: Boolean,
    onClick: () -> Unit
) {
    AppClickableSurface(
        modifier = modifier.heightIn(max = 50.dp),
        tonalElevation = Dimensions.Paddings.small,
        shadowElevation = 0.dp,
        enabled = enabled,
        onClick = onClick
    ) {
        if (image == null) {
            EmptySide()
        } else {
            DocumentSide(
                name = name,
                dateOfBirth = dateOfBirth,
                image = image.bitmap,
                type = type,
                expiryDate = expiryDate,
                page = page,
                documentNumber = documentNumber
            )
        }
    }
}

@Composable
private fun BoxScope.EmptySide() {
    AppColumn(
        modifier = Modifier.align(Alignment.Center),
        alignment = Alignment.CenterHorizontally
    ) {
        AppIcon(
            modifier = Modifier
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.secondary)
                .padding(Dimensions.Paddings.medium),
            painter = rememberVectorPainter(image = Icons.Default.Add)
        )

        AppBodyText(text = stringResource(id = R.string.capture_document))
    }
}

@Composable
private fun BoxScope.DocumentSide(
    name: String?,
    dateOfBirth: String?,
    image: Bitmap,
    type: String?,
    expiryDate: String?,
    documentNumber: String?,
    page: Int
) {
    AppColumn(
        modifier = Modifier
            .fillMaxWidth()
            .align(Alignment.Center),
        arrangement = Arrangement.Top,
        alignment = Alignment.Start
    ) {
//        if (!name.isNullOrBlank()) {
//            AppBodyText(text = stringResource(id = R.string.document_name) + ": " + name)
//        }
//
//        if (!dateOfBirth.isNullOrBlank()) {
//            AppBodyText(text = stringResource(id = R.string.document_date_of_birth) + ": " + dateOfBirth)
//        }
//
//        AppRow {
//            if (!expiryDate.isNullOrBlank()) {
//                AppBodyText(text = stringResource(id = R.string.document_expiry) + ": ")
//            }
//
//            if (!type.isNullOrBlank()) {
//                AppBodyText(text = type)
//            }
//
//            if (!expiryDate.isNullOrBlank()) {
//                AppBodyText(text = expiryDate)
//            }
//        }

        AppBitmapImage(
            modifier = Modifier
                .fillMaxWidth()
                .clip(MaterialTheme.shapes.medium),
            bitmap = image,
            contentScale = ContentScale.FillBounds
        )
    }
}
