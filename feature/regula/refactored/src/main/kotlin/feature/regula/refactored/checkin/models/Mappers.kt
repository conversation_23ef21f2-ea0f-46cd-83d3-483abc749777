package feature.regula.refactored.checkin.models

import core.common.status.Status
import domain.regula.models.ScreenState

internal fun ScreenState.toUIState(): UIScreenState {
    if (status.any { it is Status.Loading }) return UILoadingState(status = status)

    return UIReadyState(
        portrait = frontSide?.portrait ?: backSide?.portrait
        ?: throw IllegalStateException("Portrait not found"),
        documentType = frontSide?.typeDescription ?: backSide?.typeDescription
        ?: throw IllegalStateException("Document type not found"),
        expiryDate = frontSide?.expiryDate ?: backSide?.expiryDate
        ?: throw IllegalStateException("Expiry date not found"),
        name = frontSide?.fullName ?: backSide?.fullName ?: throw IllegalStateException("Name not found"),
        status = status,
        verification = faceVerificationImage?.image ?: throw IllegalStateException("Face verification not found"),
        matchType = if (similarityResults == null) {
            MatchType.NONE
        } else if (similarityResults!!.similarity >= 0.75) {
            MatchType.FULL
        } else if (similarityResults!!.similarity > 0.3 && similarityResults!!.similarity < 0.75) {
            MatchType.PARTIAL
        } else {
            MatchType.NONE
        },
        frontSide = frontSide?.image ?: throw IllegalStateException("Front side not found"),
        backSide = backSide?.image,
        documentNumber = frontSide?.documentNumber ?: backSide?.documentNumber
        ?: throw IllegalStateException("Document number not found"),
        similarity = similarityResults?.similarity ?: 0.0,
        extra = extra,
        bookingNumber = bookingNumber
    )
}