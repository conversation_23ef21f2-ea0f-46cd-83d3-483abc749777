package feature.regula.refactored.face.models

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import core.common.status.Status
import domain.regula.models.ScreenState
import feature.common.compose.dropdown.AppDropDownItem
import keyless.feature.regula.refactored.R

internal fun ScreenState.toUIState(): UIScreenState {
    if (status.any { it is Status.Loading }) return UILoadingState(status = status)

    return UIReadyState(
        status = status,
        image = faceVerificationImage?.image
    )
}