package feature.regula.refactored.face.models

import core.common.status.Status
import core.regula.common.models.RegulaImage

sealed interface UIScreenState {
    val status: List<Status>
}

internal data class UILoadingState(
    override val status: List<Status>
) : UIScreenState


internal data class UIReadyState(
    val image: RegulaImage?,
    override val status: List<Status>
) : UIScreenState {
    val isConfirmEnabled: Boolean = image != null
}