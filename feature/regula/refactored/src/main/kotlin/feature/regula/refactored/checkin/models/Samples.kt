package feature.regula.refactored.checkin.models

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.drawable.toBitmap
import core.regula.common.models.RegulaImage
import keyless.feature.regula.refactored.R

internal object Samples {

    fun initState(): UIScreenState {
        return UILoadingState(status = emptyList())
    }

    @Composable
    fun sampleState(context: Context): UIScreenState {
        val image = ResourcesCompat.getDrawable(context.resources, R.drawable.ic_document_success, context.theme)
        return UIReadyState(
            status = emptyList(),
            portrait = RegulaImage(image!!.toBitmap()),
            verification = RegulaImage(image!!.toBitmap()),
            matchType = MatchType.PARTIAL,
            name = "Some name example",
            expiryDate = "1998-01-01",
            documentType = "Passport",
            frontSide = RegulaImage(image!!.toBitmap()),
            backSide = RegulaImage(image!!.toBitmap()),
            documentNumber = "*********",
            similarity = 0.8,
            extra = "Some extra data",
            bookingNumber = ""
        )
    }
}