package feature.routines

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import data.common.preferences.Preferences
import data.common.preferences.Roles
import data.network.android.RoutineModel
import data.utils.android.CommonValues
import data.utils.android.settings.SharedPreferenceUtils
import keyless.feature.routines.databinding.RoutineMainAdapterLayBinding

class RoutinesAdapter(contextMain: Fragment, var param: String) :
    RecyclerView.Adapter<RoutinesAdapter.ViewHolder>() {

    var routineData: ArrayList<RoutineModel> = ArrayList()
    lateinit var context: Context
    var listener = contextMain as ClickToEdit

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = RoutineMainAdapterLayBinding.inflate(LayoutInflater.from(context), viewGroup, false)
        return ViewHolder(binding)
    }

    override fun getItemCount() = routineData.size

    @SuppressLint("SetTextI18n")
    @RequiresApi(Build.VERSION_CODES.O)
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(routineData[position])
    }

    fun updateRoutines(locks: ArrayList<RoutineModel>) {
        routineData = locks
        notifyDataSetChanged()
    }

    inner class ViewHolder(val binding: RoutineMainAdapterLayBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: RoutineModel) {
            if (param == "onlyList") {
                binding.arrow.isVisible = false
                binding.txtPersons.isVisible = false
                binding.txtDoors.isVisible = false
            } else {
                binding.txtPersons.isVisible = true
                binding.txtDoors.isVisible = true
                if (model.totalPersons == 0 || model.totalPersons == 1) {
                    binding.txtPersons.text =
                        model.totalPersons.toString() + " " + context.getString(
                        keyless.data.utils.android.R.string.text_person
                    )
                } else {
                    binding.txtPersons.text =
                        model.totalPersons.toString() + " " + context.getString(
                        keyless.data.utils.android.R.string.text_persons
                    )
                }

                if (model.totalLocks == 1) {
                    binding.txtDoors.text =
                        model.totalLocks.toString() + " " + context.getString(
                        keyless.data.utils.android.R.string.text_door
                    )
                } else {
                    binding.txtDoors.text =
                        model.totalLocks.toString() + " " + context.getString(
                        keyless.data.utils.android.R.string.text_doors
                    )
                }

                binding.arrow.isVisible = !(
                    Preferences.role.get() == Roles.VIEWER_ACCESS ||
                        Preferences.role.get() == Roles.CUSTOMER_SERVICES
                    )
            }
            binding.txtName.text = model.name

            binding.mainLay.setOnClickListener {
                if (!(
                    Preferences.role.get() == Roles.CUSTOMER_SERVICES ||
                        Preferences.role.get() == Roles.VIEWER_ACCESS
                    )
                ) {
                    listener.editRoutine(model, param)
                }
            }
        }
    }

    interface ClickToEdit {
        fun editRoutine(_id: RoutineModel, param: String)
    }
}