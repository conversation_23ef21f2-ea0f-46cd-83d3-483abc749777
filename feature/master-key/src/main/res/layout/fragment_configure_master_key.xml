<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context="feature.masterkey.ConfigureMasterKeyFragment">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                style="@style/ImageMirror"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/iv_back_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/viewBack"
                android:layout_width="30dp"
                android:layout_height="30dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:text="@string/configure_key"
                android:textColor="@color/white"
                android:textSize="18dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar2"
        app:layout_constraintVertical_bias="0.0">


        <TextView
            android:id="@+id/tv_brand"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/selected_key"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/etSelectedKey"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_edit_corners"
            android:editable="false"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/selected_key"
            android:includeFontPadding="false"
            android:padding="14dp"
            android:textColor="@color/black"
            android:textSize="16dp"
            style="@style/mirrorText"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_brand"
            tools:ignore="Deprecated" />

        <TextView
            android:id="@+id/tv_lock_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/select_lock"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/etSelectedKey" />

        <Spinner
            android:id="@+id/txtSelectLock"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_edit_corners"
            android:clickable="true"
            style="@style/mirrorText"
            android:drawableEnd="@drawable/ic_arrow_downward"
            android:editable="false"
            android:focusable="false"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/select_lock"
            android:includeFontPadding="false"
            android:padding="14dp"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_lock_name" />

        <TextView
            android:id="@+id/tv_building_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/valid_till"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtSelectLock" />

        <EditText
            android:id="@+id/selectTimeZone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_edit_corners"
            android:clickable="true"
            android:drawableEnd="@drawable/ic_arrow_downward"
            android:editable="false"
            android:focusable="false"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/valid_till"
            android:includeFontPadding="false"
            android:minWidth="400dp"
            android:padding="14dp"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="16dp"
            style="@style/mirrorText"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_building_name" />



        <TextView
            android:id="@+id/btnConfigure"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="60dp"
            android:layout_marginTop="43dp"
            android:layout_marginEnd="60dp"
            android:layout_marginBottom="41dp"
            android:background="@drawable/bg_btn_round"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:text="@string/configure"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:background="#33000000"
        android:id="@+id/progressLay"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        android:layout_height="match_parent">


        <LinearLayout
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:gravity="center"
            android:background="@drawable/white_all_corners_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_gravity="center"
                android:theme="@style/progressBarBlue"
                android:progressTint="@color/colorAccent"
                android:progressBackgroundTint="@color/colorAccent"
                android:layout_height="wrap_content"/>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>





</androidx.constraintlayout.widget.ConstraintLayout>