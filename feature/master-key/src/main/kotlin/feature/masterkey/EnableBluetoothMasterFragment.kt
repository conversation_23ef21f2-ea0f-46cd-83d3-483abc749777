package feature.masterkey

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import data.utils.android.CommonValues
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import keyless.feature.master.key.R
import keyless.feature.master.key.databinding.FragmentEnableBluetoothMasterkeyBinding

class EnableBluetoothMasterFragment : Fragment() {

    private lateinit var binding: FragmentEnableBluetoothMasterkeyBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentEnableBluetoothMasterkeyBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        clickListeners()
    }

    private fun askPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.BLUETOOTH_CONNECT
                ),
                50
            )
        } else {
            if (CommonValues.isBluetoothEnabled()) {
                CommonValues.loadFragment(
                    fragment = FindMasterKeyFragment(),
                    supportFragmentManager = requireFragmentManager(),
                    layoutId = R.id.frameContainerMaster
                )
            } else {
                val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                if (!mBluetoothAdapter.isEnabled) {
                    val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                    startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                } else {
                    val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                    startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                }
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (
                    grantResults.isNotEmpty() &&
                    grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                    grantResults[1] == PackageManager.PERMISSION_GRANTED
                ) {
                    if (CommonValues.isBluetoothEnabled()) {
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                } else {
                    showDialogForPermissions()
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10) {
            askPermission()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode != 0) {
            CommonValues.loadFragment(
                fragment = FindMasterKeyFragment(),
                supportFragmentManager = requireFragmentManager(),
                layoutId = R.id.frameContainerMaster
            )
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode == 0) {
            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
        }
    }

    private fun showDialogForPermissions() {
        defaultDialog(
            requireActivity(),
            getString(keyless.data.utils.android.R.string.please_allow_bluetooth),
            object : OnActionOK {
                override fun onClickData() {
                    if (activity != null && isAdded) {
                        startActivityForResult(
                            Intent(
                                Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                Uri.fromParts("package", requireActivity().packageName, null)
                            ),
                            10
                        )
                    }
                }
            }
        )
    }

    private fun clickListeners() {
        binding.btnNext.setOnClickListener {
            askPermission()
        }
    }
}