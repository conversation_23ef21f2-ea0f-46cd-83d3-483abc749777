package feature.dfu.data

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import feature.dfu.repo.DeviceFirmUpdateService
import no.nordicsemi.android.common.logger.NordicLogger
import no.nordicsemi.android.common.logger.NordicLoggerFactory
import no.nordicsemi.android.dfu.DfuServiceController
import no.nordicsemi.android.dfu.DfuServiceInitiator
import no.nordicsemi.android.dfu.DfuServiceListenerHelper
import javax.inject.Inject

class DeviceFirmUpdateManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val loggerFactory: NordicLoggerFactory
) {

    private var logger: NordicLogger? = null

//    fun install(
//        file : FirmwareZipFile,
//        target : DeviceTarget,
//        settings: DeviceFirmUpdateSettings
//    ) : DfuServiceController {
//
//        logger = loggerFactory
//            .create(null , target.device.address , target.name)
//            .also {
//                DfuServiceListenerHelper.registerLogListener(context) { _, level, message ->
//                    it.log(level, message)
//                }
//            }
//
//        val starter = DfuServiceInitiator(target.device.address).apply {
//
//            setDeviceName(target.name)
//
//            setKeepBond(settings.keepBondInformation)
//            setForceDfu(settings.externalMcuDfu)
//
//            if (settings.disableResume) {
//                disableResume()
//            }
//
//            setForceScanningForNewAddressInLegacyDfu(settings.forceScanningInLegacyDfu)
//            setPrepareDataObjectDelay(settings.prepareDataObjectDelay.toLong())
//            setRebootTime(settings.rebootTime.toLong())
//            setScanTimeout(settings.scanTimeout.toLong())
//            setUnsafeExperimentalButtonlessServiceInSecureDfuEnabled(true)
//
//            setPacketsReceiptNotificationsEnabled(settings.packetsReceiptNotification)
//            setPacketsReceiptNotificationsValue(settings.numberOfPackets)
//        }
//
//        starter.setZip(file.uri , null)
//
//        DfuServiceInitiator.createDfuNotificationChannel(context)
//
//        return starter.start(context , DeviceFirmUpdateService::class.java)
//    }

    fun install(
        file: FirmwareZipFile,
        target: DeviceTarget,
        settings: DeviceFirmUpdateSettings
    ): DfuServiceController {
        logger = loggerFactory
            .create(null, target.deviceMac!!, target.deviceName)
            .also {
                DfuServiceListenerHelper.registerLogListener(context) { _, level, message ->
                    it.log(level, message)
                }
            }

        val starter = DfuServiceInitiator(target.deviceMac).apply {
            setDeviceName(target.deviceName)

            setKeepBond(settings.keepBondInformation)
            setForceDfu(settings.externalMcuDfu)

            if (settings.disableResume) {
                disableResume()
            }

//            setForceScanningForNewAddressInLegacyDfu(settings.forceScanningInLegacyDfu)
            setPrepareDataObjectDelay(settings.prepareDataObjectDelay.toLong())
            setRebootTime(settings.rebootTime.toLong())
            setScanTimeout(settings.scanTimeout.toLong())
            setUnsafeExperimentalButtonlessServiceInSecureDfuEnabled(true)

            setPacketsReceiptNotificationsEnabled(settings.packetsReceiptNotification)
            setPacketsReceiptNotificationsValue(settings.numberOfPackets)
        }

        starter.setZip(file.uri, null)
        return starter.start(context, DeviceFirmUpdateService::class.java)
    }

    fun install(
        file: FirmwareZipFile,
        target: DeviceTarget
    ): DfuServiceController {
        val starter = DfuServiceInitiator(target.deviceMac!!)
        starter.setDeviceName(target.deviceName)
        starter.setKeepBond(false)
        starter.setForceDfu(false)
        starter.setForeground(false)
        starter.setZip(file.uri, file.path)

        DfuServiceInitiator.createDfuNotificationChannel(context)

        return starter.start(context, DeviceFirmUpdateService::class.java)
    }

    fun openLogger() {
        NordicLogger.launch(context, logger)
    }
}