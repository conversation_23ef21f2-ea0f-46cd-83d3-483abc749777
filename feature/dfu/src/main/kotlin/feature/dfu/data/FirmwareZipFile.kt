package feature.dfu.data

import android.net.Uri
import android.os.Parcel
import android.os.Parcelable

class FirmwareZipFile(
    var uri: Uri?,
    val name: String?,
    val path: String?,
    val size: Long
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readParcelable(Uri::class.java.classLoader),
        parcel.readString(),
        parcel.readString(),
        parcel.readLong()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeParcelable(uri, flags)
        parcel.writeString(name)
        parcel.writeString(path)
        parcel.writeLong(size)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<FirmwareZipFile> {
        override fun createFromParcel(parcel: Parcel): FirmwareZipFile {
            return FirmwareZipFile(parcel)
        }

        override fun newArray(size: Int): Array<FirmwareZipFile?> {
            return arrayOfNulls(size)
        }
    }
}