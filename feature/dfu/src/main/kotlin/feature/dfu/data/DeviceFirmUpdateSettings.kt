package feature.dfu.data

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

internal const val NUMBER_OF_POCKETS_INITIAL = 12

@Keep
data class DeviceFirmUpdateSettings(
    val packetsReceiptNotification: Boolean = false,
    val numberOfPackets: Int = NUMBER_OF_POCKETS_INITIAL,
    val keepBondInformation: Boolean = false,
    val externalMcuDfu: Boolean = false,
    val disableResume: Boolean = false,
    val prepareDataObjectDelay: Int = 400, // ms
    val rebootTime: Int = 0, // ms
    val scanTimeout: Int = 2_000, // ms
    val forceScanningInLegacyDfu: Boolean = false,
    val showWelcomeScreen: Boolean = true
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readByte() != 0.toByte(),
        parcel.readInt(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeByte(if (packetsReceiptNotification) 1 else 0)
        parcel.writeInt(numberOfPackets)
        parcel.writeByte(if (keepBondInformation) 1 else 0)
        parcel.writeByte(if (externalMcuDfu) 1 else 0)
        parcel.writeByte(if (disableResume) 1 else 0)
        parcel.writeInt(prepareDataObjectDelay)
        parcel.writeInt(rebootTime)
        parcel.writeInt(scanTimeout)
        parcel.writeByte(if (forceScanningInLegacyDfu) 1 else 0)
        parcel.writeByte(if (showWelcomeScreen) 1 else 0)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<DeviceFirmUpdateSettings> {
        override fun createFromParcel(parcel: Parcel): DeviceFirmUpdateSettings {
            return DeviceFirmUpdateSettings(parcel)
        }

        override fun newArray(size: Int): Array<DeviceFirmUpdateSettings?> {
            return arrayOfNulls(size)
        }
    }
}