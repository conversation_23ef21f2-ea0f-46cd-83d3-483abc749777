package feature.dfu.data

import android.bluetooth.BluetoothDevice
import android.os.Parcel
import android.os.Parcelable

data class DeviceTarget122(
    val device: BluetoothDevice,
    val name: String
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readParcelable(BluetoothDevice::class.java.classLoader)!!,
        parcel.readString()!!
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeParcelable(device, flags)
        parcel.writeString(name)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<DeviceTarget122> {
        override fun createFromParcel(parcel: Parcel): DeviceTarget122 {
            return DeviceTarget122(parcel)
        }

        override fun newArray(size: Int): Array<DeviceTarget122?> {
            return arrayOfNulls(size)
        }
    }
}

data class DeviceTarget(
    val deviceName: String?,
    val deviceMac: String?
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(deviceName)
        parcel.writeString(deviceMac)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<DeviceTarget> {
        override fun createFromParcel(parcel: Parcel): DeviceTarget {
            return DeviceTarget(parcel)
        }

        override fun newArray(size: Int): Array<DeviceTarget?> {
            return arrayOfNulls(size)
        }
    }
}