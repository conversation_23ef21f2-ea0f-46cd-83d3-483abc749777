package feature.dfu.data

import android.content.Context
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import no.nordicsemi.android.dfu.DfuBaseService
import no.nordicsemi.android.dfu.DfuProgressListenerAdapter
import no.nordicsemi.android.dfu.DfuServiceListenerHelper
import no.nordicsemi.android.error.SecureDfuError
import javax.inject.Inject

class DfuProgressManager @Inject constructor(
    @ApplicationContext private val context: Context
) : DfuProgressListenerAdapter() {
    val status = MutableStateFlow<DfuState>(DfuState.Idle)

    override fun onEnablingDfuMode(deviceAddress: String) {
        status.value = DfuState.InProgress(InitializingDFU)
    }

    override fun onProgressChanged(
        deviceAddress: String,
        percent: Int,
        speed: Float,
        avgSpeed: Float,
        currentPart: Int,
        partsTotal: Int
    ) {
        status.value = DfuState.InProgress(Uploading(percent, avgSpeed, currentPart, partsTotal))
    }

    override fun onDfuCompleted(deviceAddress: String) {
        status.value = DfuState.InProgress(Completed)
    }

    override fun onDfuAborted(deviceAddress: String) {
        status.value = DfuState.InProgress(Aborted)
    }

    override fun onError(
        deviceAddress: String,
        error: Int,
        errorType: Int,
        message: String?
    ) {
        val betterMessage = when (error) {
            DfuBaseService.ERROR_DEVICE_DISCONNECTED -> context.getString(
                keyless.data.utils.android.R.string.dfu_error_link_loss
            )
            DfuBaseService.ERROR_FILE_ERROR -> context.getString(
                keyless.data.utils.android.R.string.dfu_error_file_error
            )
            DfuBaseService.ERROR_FILE_INVALID -> context.getString(
                keyless.data.utils.android.R.string.dfu_error_file_unsupported
            )
            DfuBaseService.ERROR_FILE_TYPE_UNSUPPORTED -> context.getString(
                keyless.data.utils.android.R.string.dfu_error_file_type_invalid
            )
            DfuBaseService.ERROR_SERVICE_NOT_FOUND -> context.getString(
                keyless.data.utils.android.R.string.dfu_error_not_supported
            )
            DfuBaseService.ERROR_BLUETOOTH_DISABLED -> context.getString(
                keyless.data.utils.android.R.string.dfu_error_bluetooth_disabled
            )
            DfuBaseService.ERROR_DEVICE_NOT_BONDED -> context.getString(
                keyless.data.utils.android.R.string.dfu_error_not_bonded
            )
            DfuBaseService.ERROR_INIT_PACKET_REQUIRED -> context.getString(
                keyless.data.utils.android.R.string.dfu_error_init_packet_required
            )
            // Secure DFU errors
            DfuBaseService.ERROR_REMOTE_TYPE_SECURE or SecureDfuError.INVALID_OBJECT -> context.getString(
                keyless.data.utils.android.R.string.dfu_error_invalid_object
            )
            DfuBaseService.ERROR_REMOTE_TYPE_SECURE or SecureDfuError.INSUFFICIENT_RESOURCES -> context.getString(
                keyless.data.utils.android.R.string.dfu_error_insufficient_resources
            )
            else -> message
        }
        Log.e("TAG_ON_ERROR", "onError: $betterMessage")

        status.value = DfuState.InProgress(DfuError(message!!, betterMessage))
    }

    fun registerListener() {
        DfuServiceListenerHelper.registerProgressListener(context, this)
    }

    fun unregisterListener() {
        DfuServiceListenerHelper.unregisterProgressListener(context, this)
    }

    fun start() {
        status.value = DfuState.InProgress(Starting)
    }

    fun release() {
        status.value = DfuState.Idle
    }
}