package feature.dfu.data

import android.content.Context
import android.net.Uri
import android.provider.MediaStore
import android.util.Log
import androidx.core.net.toFile
import androidx.core.net.toUri
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

class DfuFileManager @Inject constructor(
    @ApplicationContext private val context: Context
) {

    companion object {
        private const val TAG = "Dfu_File_Manager"
    }

    fun createFile(uri: Uri): FirmwareZipFile? {
        return try {
            createFromContentResolver(uri)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading file from content resolver.", e)
            null
        }
    }

    private fun createFromFile(uri: Uri): FirmwareZipFile {
        val file = uri.toFile()
        return FirmwareZipFile(uri, file.name, file.path, file.length())
    }

    private fun createFromContentResolver(uri: Uri): FirmwareZipFile? {
        val data = context.contentResolver.query(uri, null, null, null, null)

        return if (data != null && data.moveToNext()) {
            val displayNameIndex = data.getColumnIndex(MediaStore.MediaColumns.DISPLAY_NAME)
            val fileSizeIndex = data.getColumnIndex(MediaStore.MediaColumns.SIZE)
            val dataIndex = data.getColumnIndex(MediaStore.MediaColumns.DATA)

            val fileName = data.getString(displayNameIndex)
            val fileSize = data.getInt(fileSizeIndex)
            val filePath = if (dataIndex != -1) {
                data.getString(dataIndex)
            } else {
                null
            }

            data.close()

            FirmwareZipFile(uri, fileName, filePath, fileSize.toLong())
        } else {
            Log.d(TAG, "Data loaded from ContentResolver is empty.")
            null
        }
    }

    fun createFile(uri: Uri, context: Context): FirmwareZipFile? {
        return try {
            val file = feature.dfu.Utils.FileValidationUtils.getFile(context, uri)
            FirmwareZipFile(file!!.toUri(), file.name, file.path, file.length())
        } catch (e: Exception) {
            Log.e(TAG, "Error loading file from content resolver.", e)
            null
        }
    }
}