package feature.dfu.view

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Column
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import no.nordicsemi.android.common.theme.view.WizardStepAction
import no.nordicsemi.android.common.theme.view.WizardStepComponent
import no.nordicsemi.android.common.theme.view.WizardStepState
import java.io.File

private const val FILE_NAME_FORMAT = "Name: <b>%s</b>"
private const val FILE_SIZE_FORMAT = "Size: <b>%d</b> bytes"

@Composable
fun FileSelectView() {
    val context = LocalContext.current
    var isFileSelected by rememberSaveable { mutableStateOf(false) }
    var fileUri by rememberSaveable { mutableStateOf<Uri>(Uri.parse("")) }
    var file by remember { mutableStateOf<File?>(File("")) }

    val launcher = rememberLauncherForActivityResult(ActivityResultContracts.GetContent()) { uri ->
        uri?.let {
            file = feature.dfu.Utils.FileValidationUtils.getFile(context, it)
            fileUri = it
            isFileSelected = true
        }
//        uri?.let {
// //            onEvent(OnZipFileSelected(it))
//        }
    }

//    when(isFileSelected){
//        false -> WhenNotSelected{
//            launcher.launch("application/zip")
//        }
//        true -> NearbyDeviceDFU(file)
//    }
}

@Composable
fun WhenSelected(fileUri: File?) {
    WizardStepComponent(
        icon = Icons.Default.Add,
        title = "Choose File",
        decor = WizardStepAction.Action(
            text = "Select File",
            onClick = {}
        ),
        state = WizardStepState.CURRENT
    ) {
        Column(
            horizontalAlignment = Alignment.Start
        ) {
            Text(
                text = String.format(FILE_NAME_FORMAT, fileUri?.name),
                style = MaterialTheme.typography.bodyMedium
            )
            Text(
                text = "${fileUri?.canonicalPath}",
                style = MaterialTheme.typography.bodyMedium
            )

            Text(
                text = String.format(FILE_SIZE_FORMAT, fileUri?.length()),
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

@Preview(showSystemUi = true, showBackground = true)
@Composable
fun PrevFileSelect() {
    FileSelectView()
}