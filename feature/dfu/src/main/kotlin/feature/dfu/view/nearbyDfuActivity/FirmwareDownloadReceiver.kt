package feature.dfu.view.nearbyDfuActivity

import android.annotation.SuppressLint
import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import org.greenrobot.eventbus.EventBus

class FirmwareDownloadReceiver : BroadcastReceiver() {

    @SuppressLint("Range")
    override fun onReceive(context: Context, intent: Intent) {
        Log.e("TAG", "OnReceiver")
        // This method is called when the BroadcastReceiver is receiving an Intent broadcast.
        if (intent.action == "android.intent.action.DOWNLOAD_COMPLETE") {
            Log.e("TAG", "onReceive: ${intent.action}")
            val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1L)
            val query = DownloadManager.Query()
            query.setFilterById(intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, 0))
            val manager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            val cursor = manager.query(query)

            if (cursor.moveToFirst()) {
                if (cursor.count > 0) {
                    val status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS))
                    val download_id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, 0)

                    if (status == DownloadManager.STATUS_SUCCESSFUL) {
//                        val file = cursor.getString(cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_FILENAME))
                        // file contains downloaded file name
                        // do your stuff here on download success
                        Log.e("TAG", "STATUS_SUCCESSFULLY: ")
                        EventBus.getDefault().post(DownloadStatus(true))
                    }

                    if (status == DownloadManager.STATUS_FAILED) {
                        Log.e("TAG", "STATUS_FAILED")
                        EventBus.getDefault().post(DownloadStatus(false))
                    }
                }
            }
            cursor.close()
        }
//        if(intent?.action == "android.intent.action.DOWNLOAD_COMPLETE") {
//            Log.e("TAG", "File Downloaded")
//            val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1L)
//            if(id != -1L) {
//                Log.e("BROADCAST", "Download with ID $id finished!")
//                EventBus.getDefault().post(DownloadStatus(true))
//            }
//        }
    }
}

data class DownloadStatus(
    var isCompleted: Boolean = false
)