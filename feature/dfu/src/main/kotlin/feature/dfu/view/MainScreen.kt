package feature.dfu.view

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.util.Log
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.outlined.Settings
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import feature.dfu.view.nearbyDfuActivity.NearByDfuDeviceActivity
import keyless.feature.dfu.R
import no.nordicsemi.android.common.theme.view.NordicAppBar
import no.nordicsemi.android.common.theme.view.WizardStepAction
import no.nordicsemi.android.common.theme.view.WizardStepComponent
import no.nordicsemi.android.common.theme.view.WizardStepState
import java.io.File

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ForPrev(callback: (file: File, name: String, mac: String) -> Unit) {
    val context = LocalContext.current

    var fileState by remember { mutableStateOf<FileState>(FileState.UNSELECTED) }
    var file by remember { mutableStateOf<File?>(File("")) }
    var fileUri by rememberSaveable { mutableStateOf<Uri>(Uri.parse("")) }
    val launcher = rememberLauncherForActivityResult(ActivityResultContracts.GetContent()) { uri ->
        uri?.let {
            file = feature.dfu.Utils.FileValidationUtils.getFile(context, it)
            fileUri = it
            fileState = FileState.SELECTED
        }
        callback.invoke(
            File(""),
            "",
            ""
        )
    }
    val deviceLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->

        if (result.resultCode == Activity.RESULT_OK) {
            val bleName = result.data?.getStringExtra("ble_name")
            val bleMac = result.data?.getStringExtra("ble_mac")
            Log.e("Result from device select", "ForPrev: $bleName")

            callback.invoke(
                file!!,
                bleName!!,
                bleMac!!
            )
            fileState = FileState.UNSELECTED
//            viewModel.onInstallButtonClick(file!! , bleName!! , bleMac!! )
//            fileState = FileState.READY_TO_UPDATE
        }
    }

    when (fileState) {
        FileState.UNSELECTED -> {
            NormalView { launcher.launch("application/zip") }
        }

        FileState.SELECTED -> {
            deviceLauncher.launch((Intent(context, NearByDfuDeviceActivity::class.java).putExtra("installer", "0")))
        }

        FileState.READY_TO_UPDATE -> {
            UpdateFirmware()
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NormalView(onFileSelect: () -> Unit) {
    Column {
        NordicAppBar(
            text = "Dfu Title",
            actions = {
//                LoggerAppBarIcon(onClick = {
// //                    onEvent(OnLoggerButtonClick)
//                })
                IconButton(onClick = {
//                    onEvent(OnSettingsButtonClick)
                }) {
                    Icon(
                        imageVector = Icons.Outlined.Settings,
                        contentDescription = "Dfu Setting Action"
                    )
                }
            }
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            OutlinedCard(
                modifier = Modifier
                    .widthIn(max = 600.dp)
                    .padding(16.dp)
                    // Leave more space for the navigation bar.
                    .padding(bottom = 16.dp)
            ) {
                WizardStepComponent(
                    modifier = Modifier.padding(16.dp),
                    icon = Icons.Default.Add,
                    title = "Choose File",
                    decor = WizardStepAction.Action(
                        text = "Select File",
                        onClick = onFileSelect

                    ),
                    state = WizardStepState.CURRENT
                ) {
                    Text(
                        text = "Choose Info",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

@Composable
fun UpdateFirmware() {
}

enum class FileState {
    UNSELECTED,
    SELECTED,
    READY_TO_UPDATE
}