package feature.dfu.repo

import android.app.Activity
import android.util.Log
import dagger.hilt.android.AndroidEntryPoint
import no.nordicsemi.android.dfu.DfuBaseService
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DFUServiceRunningObserver @Inject constructor() {
    var isRunning: Boolean = false
}

@AndroidEntryPoint
class DeviceFirmUpdateService : DfuBaseService() {

    @Inject
    lateinit var serviceObserver: DFUServiceRunningObserver

    override fun getNotificationTarget(): Class<out Activity>? {
        return Class.forName("feature.dfu.view.DFUActivity") as Class<out Activity>
    }

    override fun onCreate() {
        super.onCreate()

        Log.e("TAG", "onCreate: DFU-- SERVICE ")

        serviceObserver.isRunning = true
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.e("TAG", "onDestroy: DFU-- SERVICE ")
        serviceObserver.isRunning = false
    }

    override fun isDebug(): Boolean {
        return true
    }
}