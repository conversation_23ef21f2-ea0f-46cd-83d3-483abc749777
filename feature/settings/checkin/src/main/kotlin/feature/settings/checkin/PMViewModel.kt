package feature.settings.checkin

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import domain.common.ErrorHandler
import domain.settings.checkin.injection.CheckInSettingsDomainScope
import domain.settings.checkin.models.Event
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.core.component.getScopeId
import org.koin.dsl.module
import kotlin.coroutines.CoroutineContext

class PMViewModel (
    private val viewModel: domain.settings.checkin.ViewModel,
    private val handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    coroutineContext: CoroutineContext? = null,
    private val onClear: () -> Unit
) : ViewModel() {


    private val scope = if (coroutineContext != null) viewModelScope + coroutineContext else viewModelScope

    val screenStream = viewModel.screenStream

    fun onEvent(event: Event) {
        scope.launch { handler.async { viewModel.onEvent(event) } }
    }

    override fun onCleared() {
        onClear()
        super.onCleared()
    }
}

val checkInFeatureModule =  module {
    viewModel {
        val scope = getKoin().getOrCreateScope<CheckInSettingsDomainScope>(CheckInSettingsDomainScope.getScopeId())

        PMViewModel(
            viewModel = scope.get(),
            handler = scope.get(),
            onClear = { scope.close() }
        )
    }
}