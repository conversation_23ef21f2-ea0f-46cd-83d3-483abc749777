<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/mainLay"
    android:layout_height="wrap_content">


    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:text="cjkhcjhjc"
        android:id="@+id/guestName"
        style="@style/mirrorText"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/lockName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:fontFamily="@font/poppins_regular_400"
        android:text="lock Name"
        android:textColor="@color/black"
        android:textSize="14dp"
        style="@style/mirrorText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guestName" />

    <TextView
        android:id="@+id/bookingNumber"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:fontFamily="@font/poppins_regular_400"
        android:text="Booking Number"
        android:textColor="@color/black"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        style="@style/mirrorText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lockName" />


    <TextView
        android:id="@+id/bookingDate"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:fontFamily="@font/poppins_regular_400"
        android:text="Booking Date"
        android:textColor="@color/black"
        android:textSize="14dp"
        style="@style/mirrorText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bookingNumber" />


    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:background="@color/line_bg_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bookingDate"
        app:layout_constraintVertical_bias="0.0" />





</androidx.constraintlayout.widget.ConstraintLayout>