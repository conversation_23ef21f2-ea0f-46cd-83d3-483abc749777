<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context="feature.settings.common.MoreSettingsFragment">

    <ImageView
        android:id="@+id/backBtnSupport"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/ImageMirror"
        android:padding="20dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/propertyTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/about_us"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtnSupport"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtnSupport" />

    <FrameLayout
        android:layout_marginTop="10dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtnSupport"
        app:layout_constraintVertical_bias="0.0">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:elevation="0dp"
            app:cardCornerRadius="32dp"
            app:cardElevation="0dp">


            <TextView
                android:id="@+id/txtValue2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/terms_of_use"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="@+id/txtValue7"
                app:layout_constraintTop_toBottomOf="@+id/view7" />

            <View
                android:id="@+id/view2"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="18dp"
                android:background="@color/line_bg_color"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/txtValue2"
                app:layout_constraintTop_toBottomOf="@+id/txtValue2" />

            <View
                android:id="@+id/termsBtn"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toTopOf="@+id/privacyBtn"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view7" />


            <TextView
                android:id="@+id/txtValue3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/privacy"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="@+id/txtValue2"
                app:layout_constraintTop_toBottomOf="@id/view2" />

            <View
                android:id="@+id/view3"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="18dp"
                android:background="@color/line_bg_color"
                app:layout_constraintEnd_toEndOf="@+id/view2"
                app:layout_constraintStart_toStartOf="@+id/txtValue3"
                app:layout_constraintTop_toBottomOf="@+id/txtValue3" />

            <View
                android:id="@+id/privacyBtn"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toTopOf="@id/view3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/view2" />


            <TextView
                android:id="@+id/txtValue7"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="24dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/about_us"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/view7"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="18dp"
                android:layout_marginEnd="20dp"
                android:background="@color/line_bg_color"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/txtValue7"
                app:layout_constraintTop_toBottomOf="@+id/txtValue7" />

            <View
                android:id="@+id/aboutUsBtn"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toTopOf="@id/view7"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />



        </androidx.constraintlayout.widget.ConstraintLayout>

    </FrameLayout>



</androidx.constraintlayout.widget.ConstraintLayout>