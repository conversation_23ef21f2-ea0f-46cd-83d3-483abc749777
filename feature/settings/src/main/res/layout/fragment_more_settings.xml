<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    tools:context="feature.settings.common.SupportActivity">


    <ImageView
        android:id="@+id/imageView12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        style="@style/ImageMirror"
        android:src="@drawable/iv_back_white"
        app:layout_constraintBottom_toBottomOf="@+id/moreTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/moreTitle" />


    <TextView
        android:id="@+id/moreTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="32dp"
        android:includeFontPadding="false"
        android:fontFamily="@font/poppins_bold_700"
        android:gravity="center"
        android:text="@string/more"
        android:textColor="@color/white"
        android:textSize="24dp"
        app:layout_constraintStart_toEndOf="@+id/imageView12"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/moreTitle">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="24dp"
            app:layout_constraintBottom_toTopOf="@+id/logout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:elevation="0dp"
                app:cardCornerRadius="32dp"
                app:cardElevation="0dp">


                <TextView
                    android:id="@+id/txtValue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="28dp"
                    android:drawableStart="@drawable/profile_img"
                    android:drawablePadding="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/my_profile"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/view1"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/line_bg_color"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/txtValue"
                    app:layout_constraintTop_toBottomOf="@+id/txtValue" />

                <View
                    android:id="@+id/myProfileBtn"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="@id/view1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <TextView
                    android:id="@+id/txtValue2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:drawableStart="@drawable/company_img"
                    android:drawablePadding="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/company_profile"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/view1" />

                <View
                    android:id="@+id/view2"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/line_bg_color"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/txtValue2"
                    app:layout_constraintTop_toBottomOf="@+id/txtValue2" />

                <View
                    android:id="@+id/companyProfileBtn"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@id/view2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/view1" />


                <TextView
                    android:id="@+id/txtCheckIn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:drawableStart="@drawable/ic_pm_check_in"
                    android:drawablePadding="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/check_in_"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/view2" />

                <View
                    android:id="@+id/viewCheckIn"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/line_bg_color"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/txtCheckIn"
                    app:layout_constraintTop_toBottomOf="@+id/txtCheckIn" />

                <View
                    android:id="@+id/checkInViewBtn"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@+id/viewCheckIn"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/view2" />


                <TextView
                    android:id="@+id/txtValue10"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:drawableStart="@drawable/iv_manage_card"
                    android:drawablePadding="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/manage_cards"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/checkInViewBtn" />

                <View
                    android:id="@+id/view12"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/line_bg_color"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/txtValue10"
                    app:layout_constraintTop_toBottomOf="@+id/txtValue10" />

                <View
                    android:id="@+id/manageCardsView"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@id/view12"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/viewCheckIn" />






                <TextView
                    android:id="@+id/txtValue3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:drawableStart="@drawable/manage_img"
                    android:drawablePadding="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/manage_staff"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/view12" />

                <View
                    android:id="@+id/view3"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/line_bg_color"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/txtValue3"
                    app:layout_constraintTop_toBottomOf="@+id/txtValue3" />

                <View
                    android:id="@+id/manageStaffBtn"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@id/view3"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/view12" />


                <TextView
                    android:id="@+id/txtValue23"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:drawableStart="@drawable/iv_maintenance"
                    android:drawablePadding="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/maintenance"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:visibility="visible"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/view3" />

                <View
                    android:id="@+id/view23"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/line_bg_color"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/txtValue23"
                    app:layout_constraintTop_toBottomOf="@+id/txtValue23" />

                <View
                    android:id="@+id/maintenanceStaffBtn"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toTopOf="@id/view23"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/view3" />


                <TextView
                    android:id="@+id/txtValue5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:drawableStart="@drawable/enbale_screen_lock_img"
                    android:drawablePadding="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/enable_screen_lock"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/view23" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/simpleSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginEnd="20dp"
                    android:textSize="15sp"
                    android:theme="@style/SCBSwitch"
                    app:backgroundColorOnSwitchOff="@color/color_grey"
                    app:backgroundColorOnSwitchOn="@color/colorAccent"
                    app:layout_constraintBottom_toBottomOf="@+id/txtValue5"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/txtValue5"
                    app:showText="false"
                    app:strokeColorOnSwitchOff="@color/colorAccent"
                    app:strokeColorOnSwitchOn="@color/colorAccent"
                    app:textColorOnSwitchOff="@color/white"
                    app:textColorOnSwitchOn="@color/colorAccent"
                    app:thumbColorOnSwitchOff="#FFFFFF"
                    app:thumbColorOnSwitchOn="#FFFFFF" />

                <View
                    android:id="@+id/view5"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="18dp"
                    android:background="@color/line_bg_color"
                    app:layout_constraintEnd_toEndOf="@+id/simpleSwitch"
                    app:layout_constraintStart_toStartOf="@+id/txtValue5"
                    app:layout_constraintTop_toBottomOf="@+id/txtValue5" />

                <View
                    android:id="@+id/snableScreenLockBtn"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@id/view5"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/view23" />


                <TextView
                    android:id="@+id/signInAdmin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="28dp"
                    android:drawableStart="@drawable/profile_img"
                    android:drawablePadding="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/sign_in"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/view5" />


                <View
                    android:id="@+id/viewSignIn"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/line_bg_color"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/signInAdmin"
                    app:layout_constraintTop_toBottomOf="@+id/signInAdmin" />

                <View
                    android:id="@+id/signInView"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@+id/viewSignIn"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/view5" />


                <TextView
                    android:id="@+id/txtLanguage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:drawableStart="@drawable/iv_change_language"
                    android:drawablePadding="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/change_language"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/viewSignIn" />


                <TextView
                    android:id="@+id/selectedLanguage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:fontFamily="@font/poppins_regular_400"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:textColor="@color/black"
                    android:textSize="12dp"
                    app:layout_constraintBottom_toBottomOf="@+id/txtLanguage"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toStartOf="@+id/txtValue"
                    app:layout_constraintTop_toTopOf="@+id/txtLanguage" />


                <View
                    android:id="@+id/view10"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/line_bg_color"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/txtLanguage" />

                <View
                    android:id="@+id/viewLanguage"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@+id/view10"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/viewSignIn" />


                <TextView
                    android:id="@+id/txtValue6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:drawableStart="@drawable/support_img"
                    android:drawablePadding="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/support"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/view10" />

                <View
                    android:id="@+id/view6"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/line_bg_color"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/txtValue6"
                    app:layout_constraintTop_toBottomOf="@+id/txtValue6" />

                <View
                    android:id="@+id/supportBtn"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@+id/view6"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/view10" />


                <TextView
                    android:id="@+id/txtValue7"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:drawableStart="@drawable/about_us_img"
                    android:drawablePadding="15dp"
                    android:fontFamily="@font/poppins_semibold_600"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/about_us"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/view6" />

                <View
                    android:id="@+id/view7"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:background="@color/line_bg_color"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/txtValue7"
                    app:layout_constraintTop_toBottomOf="@+id/txtValue7" />

                <View
                    android:id="@+id/aboutUsBtn"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@id/view7"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/view6" />


                <TextView
                    android:id="@+id/versionName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/poppins_regular_400"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:textColor="@color/black"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/view7" />


                <androidx.constraintlayout.widget.Group
                    android:id="@+id/fullGroup"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="companyProfileBtn,view2,txtValue2,manageStaffBtn,view3,txtValue3" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/fullGroupMaintenance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="maintenanceStaffBtn,view23,txtValue23" />


            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>


        <TextView
            android:id="@+id/textView28"
            style="@style/buttonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@null"
            android:drawableStart="@drawable/iv_logout"
            android:drawablePadding="15dp"
            android:drawableTint="@color/black"
            android:elevation="1dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/Signout"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@+id/logout"
            app:layout_constraintEnd_toEndOf="@+id/logout"
            app:layout_constraintStart_toStartOf="@+id/logout"
            app:layout_constraintTop_toTopOf="@+id/logout" />

        <View
            android:id="@+id/logout"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_gravity="bottom"
            android:layout_marginBottom="24dp"
            android:background="@drawable/bg_btn_round"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.6" />

    </androidx.constraintlayout.widget.ConstraintLayout>




</androidx.constraintlayout.widget.ConstraintLayout>