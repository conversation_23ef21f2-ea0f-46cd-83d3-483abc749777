<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent">


    <ImageView
        android:id="@+id/backBtn"
        style="@style/ImageMirror"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:padding="20dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/titleToolbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/select_user"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtn" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:id="@+id/scrollView"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtn"
        app:layout_constraintVertical_bias="0.0">


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/lockListView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0">


                <EditText
                    android:id="@+id/svLock"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/bg_btn_round"
                    android:backgroundTint="@color/bg_edit_grey"
                    android:drawableStart="@drawable/ic_baseline_search_24"
                    android:drawablePadding="10dp"
                    android:ellipsize="end"
                    style="@style/mirrorText"
                    android:focusableInTouchMode="true"
                    android:fontFamily="@font/poppins_regular_400"
                    android:hint="@string/search_by_email"
                    android:imeOptions="actionSearch"
                    android:includeFontPadding="false"
                    android:padding="8dp"
                    android:inputType="text"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:paddingRight="30dp"
                    android:singleLine="true"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/stopSearch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:src="@drawable/iv_cross_black"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@+id/svLock"
                    app:layout_constraintEnd_toEndOf="@+id/svLock"
                    app:layout_constraintTop_toTopOf="@+id/svLock" />


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvUsers"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/svLock" />





            </androidx.constraintlayout.widget.ConstraintLayout>






    </androidx.core.widget.NestedScrollView>


    <!--    <ProgressBar-->
    <!--        android:id="@+id/progress_pagination"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginBottom="12dp"-->
    <!--        android:theme="@style/progressBarBlue"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintStart_toStartOf="parent" />-->


    <LinearLayout
        android:id="@+id/noDataView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/scrollView">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/iv_no_share_access" />

        <TextView
            android:id="@+id/noDataText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:fontFamily="@font/poppins_medium_500"
            android:includeFontPadding="false"
            android:text="@string/no_user_found"
            android:textColor="@color/black" />


    </LinearLayout>



    <include
        android:id="@+id/progressBar"
        layout="@layout/progress_lay"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>