package feature.settings.common

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import data.common.preferences.Preferences
import data.utils.android.CommonValues
import data.utils.android.settings.SharedPreferenceUtils
import feature.settings.support.nearby.NearByDeviceActivity
import feature.settings.support.diagnostics.DiagnosticsScreen
import keyless.feature.settings.databinding.ActivitySupportBinding

class SupportActivity : AppCompatActivity() {
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    private lateinit var binding: ActivitySupportBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySupportBinding.inflate(layoutInflater)
        setContentView(binding.root)
        onClicks()
        checkIsPaid()
    }

    private fun checkIsPaid() {
//        val paid = SharedPreferenceUtils.getInstance(this).isPaid
//        if (!paid){
        if (Preferences.userRole.get() == CommonValues.GUEST) {
            binding.txtValue333.visibility = View.GONE
            binding.callBtnTxt.visibility = View.GONE
            binding.callBtnView.visibility = View.GONE
            binding.furtherAssistance.visibility = View.GONE
        }
//        }
    }

    private fun onClicks() {
        binding.clickDiagnosis.setOnClickListener {
            startActivity(Intent(this, DiagnosticsScreen::class.java))
        }

        binding.callBtnView.setOnClickListener {
            CommonValues.openPhone(
                this,
                "+971 600539000"
            )
        }
        binding.backBtnSupport.setOnClickListener {
            finish()
        }
        // find near by
        binding.findNearBy.setOnClickListener {
            startActivity(Intent(this, NearByDeviceActivity::class.java))
        }
    }
}