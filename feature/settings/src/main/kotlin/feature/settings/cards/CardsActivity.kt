package feature.settings.cards

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import data.network.android.DataModelCard
import feature.common.dialogs.ProgressDialogUtils
import data.utils.android.hideKeyboard
import data.utils.android.interfaces.PaginationScrollListener
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import feature.settings.profile.ProfileViewModel
import keyless.feature.settings.databinding.ActivityCardsBinding
import org.koin.androidx.viewmodel.ext.android.viewModel

class CardsActivity : AppCompatActivity(), AdapterCards.SelectCard {

    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    private val mViewModel: ProfileViewModel by viewModel()
    private lateinit var adapterCards: AdapterCards
    private var page = 1
    private var isLastPage: Boolean = false
    var isLoadingMore = false
    private val layoutManager = LinearLayoutManager(this)
    private var listTotalCards: ArrayList<DataModelCard> = ArrayList()
    private lateinit var binding: ActivityCardsBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCardsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setAdapter()
        apiImplementation("", page)
        observerInit()
        clickListeners()
    }

    private fun pagination() {
        binding.rvCards.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
            override fun isLastPage(): Boolean {
                return isLastPage
            }

            override fun loadMoreItems() {
                if (!isLoadingMore) {
                    page++
                    apiImplementation("", page)
                    binding.progressPagination.visibility = View.VISIBLE
                }
            }

            override fun isLoading(): Boolean {
                return isLoadingMore
            }
        })
    }

    private fun clickListeners() {
        binding.backBtn.setOnClickListener {
            finish()
        }

        binding.svCards.setOnEditorActionListener(
            TextView.OnEditorActionListener { v, actionId, event ->
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    performSearch()
                    return@OnEditorActionListener true
                }
                false
            }
        )

        binding.svCards.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                binding.stopSearchCards.isVisible = p0.toString().isNotEmpty()
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })

        binding.stopSearchCards.setOnClickListener {
            hideKeyboard()
            binding.svCards.setText("")
            page = 1
            apiImplementation("", page)
        }
    }

    private fun performSearch() {
        apiImplementation(binding.svCards.text.toString(), 1)
    }

    private fun setAdapter() {
        binding.rvCards.layoutManager = layoutManager
        adapterCards = AdapterCards(this)
        binding.rvCards.adapter = adapterCards
        pagination()
    }

    private fun observerInit() {
        mViewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        mViewModel.error.observe(this) {
            toast(it)
        }
    }

    private fun apiImplementation(search: String, page: Int) {
        isLoadingMore = true
        mViewModel.getCardsList(sharePrefs.token, search, page).observe(this) {
            isLoadingMore = false
            binding.progressPagination.visibility = View.GONE
            if (it.data.size > 0) {
                if (this.page == 1) {
                    listTotalCards.clear()
                }
                listTotalCards.addAll(it.data)
                binding.layNoData.isVisible = false
                binding.rvCards.isVisible = true
                isLastPage = listTotalCards.size == it.total_count
                adapterCards.update(listTotalCards)
            } else {
                binding.layNoData.isVisible = true
                binding.rvCards.isVisible = false
            }
        }
    }

    override fun clickOnItem(modelCards: DataModelCard) {
//        val intent = Intent(this, SelectRayonicsLock::class.java)
//        intent.putExtra("modelCards", modelCards)
        setResult(50, Intent().putExtra("modelCards", modelCards))
        finish()
    }
}