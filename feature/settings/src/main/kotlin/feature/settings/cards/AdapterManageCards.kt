package feature.settings.cards

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import data.common.preferences.Preferences
import data.common.preferences.Roles
import data.network.android.DataModelCardManage
import data.utils.android.CommonValues
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import keyless.data.utils.android.R
import keyless.feature.settings.databinding.LayoutManageCardsBinding

class AdapterManageCards(contextMain: Context) :
    RecyclerView.Adapter<AdapterManageCards.ViewHolder>() {

    lateinit var context: Context
    var listNew = ArrayList<DataModelCardManage>()
    var listener = contextMain as SelectCard

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = LayoutManageCardsBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = listNew[position]
        holder.bind(model)
    }

    override fun getItemCount() = listNew.size

    fun update(it: ArrayList<DataModelCardManage>) {
        listNew = it
        notifyDataSetChanged()
    }

    inner class ViewHolder(val binding: LayoutManageCardsBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: DataModelCardManage) {
            binding.txtInternalId.text = model.internal_id
            binding.txtLockName.text = "Lock name: " + model.lock_assign.lock.name
            binding.txtAddress.text =
                "Address: " + model.appartment_number + ", " + model.appartment_number + " Floor " + model.property_name
            binding.txtAssignedBy.text =
                "Assigned By " + model.lock_assign.user.first_name + " " + model.lock_assign.user.last_name
            binding.txtValidDate.text =
                CommonValues.formateTimeDate(model.lock_assign.start_time, context) + " to " + CommonValues.formateTimeDate(
                    model.lock_assign.end_time,
                    context
                )

            if (model.lock_assign.lock.provider != CommonValues.iseo) {
                if (model.written_on == 0 || model.written_on == 1) {
                    binding.mainLay.alpha = 1f
                } else {
                    binding.mainLay.alpha = 0.6f
                }
                binding.dots.isVisible = true
            } else {
                binding.mainLay.alpha = 1f
                binding.dots.isVisible = false
            }

//        binding.dots.isVisible = model.lock_assign.lock.provider != CommonValues.iseo

            binding.txtDate.text =
                CommonValues.formateTimeDate(model.lock_assign.created_at, context)

            binding.mainLay.setOnClickListener {
                if (
                    Preferences.role.get() != Roles.CUSTOMER_SERVICES &&
                    Preferences.role.get() != Roles.VIEWER_ACCESS
                ) {
                    if (Preferences.isAdminLogin()) {
                        defaultDialog(
                            context,
                            context.getString(R.string.disabled_in_admin_mode),
                            object : OnActionOK {
                                override fun onClickData() {
                                }
                            }
                        )
                    } else {
                        if (model.lock_assign.lock.provider == CommonValues.keyless) {
                            if (model.written_on == 0 || model.written_on == 1) {
                                listener.clickToManageCard(model)
                            } else {
                                defaultDialog(
                                    context,
                                    context.getString(R.string.you_cannot_delete_this_card),
                                    object : OnActionOK {
                                        override fun onClickData() {
                                        }
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    interface SelectCard {
        fun clickToManageCard(modelCards: DataModelCardManage)
    }
}