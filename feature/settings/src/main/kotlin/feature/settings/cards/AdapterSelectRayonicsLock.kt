package feature.settings.cards

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import androidx.recyclerview.widget.RecyclerView
import data.common.preferences.Preferences
import data.network.android.LocksListResponse
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import keyless.data.utils.android.R
import keyless.feature.settings.databinding.LockOnlyRayonicLayoutBinding

class AdapterSelectRayonicsLock(
    var arrayLocks: ArrayList<LocksListResponse.LocksModel>,
    contextListener: Context
) :
    RecyclerView.Adapter<AdapterSelectRayonicsLock.ViewHolder>(), Filterable {

    lateinit var context: Context
    var listFiltered = arrayLocks
    var listener = contextListener as ClickToConnect

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = LockOnlyRayonicLayoutBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(listFiltered[position])
    }

    override fun getItemCount() = listFiltered.size

    inner class ViewHolder(val binding: LockOnlyRayonicLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: LocksListResponse.LocksModel) {
            binding.txtLockName.text = listFiltered[position].lock.name
            if (listFiltered[position].property_details.appartment_number.isEmpty()) {
                binding.txtLockPlace.text =
                    listFiltered[position].property_details.floor + " " + context.getString(
                    keyless.data.utils.android.R.string.txt_floor
                )
            } else {
                binding.txtLockPlace.text =
                    listFiltered[position].property_details.appartment_number +
                    ", " + listFiltered[position].property_details.floor +
                    " " + context.getString(keyless.data.utils.android.R.string.txt_floor)
            }

            binding.txtPropertyName.text = listFiltered[position].property_details.name

            binding.mainLay.setOnClickListener {
                if (Preferences.isAdminLogin()) {
                    defaultDialog(
                        context,
                        context.getString(R.string.disabled_in_admin_mode),
                        object : OnActionOK {
                            override fun onClickData() {
                            }
                        }
                    )
                } else {
                    listener.clickConnecting(listFiltered[position])
                }
            }
        }
    }

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                val charString = constraint?.toString() ?: ""
                listFiltered = if (charString.isEmpty()) {
                    arrayLocks
                } else {
                    val mFilteredList = ArrayList<LocksListResponse.LocksModel>()
                    arrayLocks.filter {
                        (it.lock.name.contains(constraint!!, true)) or
                            (it.lock.name.startsWith(constraint, true))
                    }
                        .forEach { mFilteredList.add(it) }
                    mFilteredList
                }
                return FilterResults().apply { values = listFiltered }
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                listFiltered = if (results?.values == null) {
                    ArrayList()
                } else {
                    results.values as java.util.ArrayList<LocksListResponse.LocksModel>
                }
                notifyDataSetChanged()
            }
        }
    }

    interface ClickToConnect {
        fun clickConnecting(locksModel: LocksListResponse.LocksModel)
    }
}