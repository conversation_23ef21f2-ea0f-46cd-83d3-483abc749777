<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/transparent"
    android:backgroundTint="@color/transparent"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <ImageView
        android:id="@+id/closeDialogBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:src="@drawable/close_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/closeDialogBtn"
        app:layout_constraintVertical_bias="0.0"
        android:layout_height="wrap_content">



    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/white_top_corners">

        <TextView
            android:id="@+id/textView15"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="25dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/current_password"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <EditText
            android:id="@+id/currentPassET"
            style="@style/mirrorText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_edit_corners"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/current_password"
            android:includeFontPadding="false"
            android:inputType="textPassword"
            android:padding="14dp"
            android:textColor="@color/black"
            android:textColorHint="@color/disable_color"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="@+id/textView15"
            app:layout_constraintTop_toBottomOf="@+id/textView15" />

        <ImageView
            android:id="@+id/showPassCurrentBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_eye_hide"
            app:layout_constraintBottom_toBottomOf="@+id/currentPassET"
            app:layout_constraintEnd_toEndOf="@+id/currentPassET"
            app:layout_constraintTop_toTopOf="@+id/currentPassET" />

        <TextView
            android:id="@+id/textView16"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/new_password"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/currentPassET" />

        <EditText
            android:id="@+id/newPassET"
            style="@style/mirrorText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_edit_corners"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/new_password"
            android:includeFontPadding="false"
            android:inputType="textPassword"
            android:padding="14dp"
            android:textColor="@color/black"
            android:textColorHint="@color/disable_color"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/textView16"
            app:layout_constraintTop_toBottomOf="@+id/textView16" />


        <ImageView
            android:id="@+id/showPassBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_eye_hide"
            app:layout_constraintBottom_toBottomOf="@+id/newPassET"
            app:layout_constraintEnd_toEndOf="@+id/newPassET"
            app:layout_constraintTop_toTopOf="@+id/newPassET" />


        <CheckBox
            android:id="@+id/check1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="10dp"
            android:button="@drawable/checkbox"
            android:checked="false"
            android:clickable="false"
            android:focusable="false"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:paddingStart="6dp"
            android:text="@string/at_least_8_characters"
            android:textColor="@color/black"
            android:textSize="13dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/newPassET" />

        <CheckBox
            android:id="@+id/check2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="10dp"
            android:button="@drawable/checkbox"
            android:checked="false"
            android:clickable="false"
            android:focusable="false"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:paddingStart="6dp"
            android:text="@string/containing_a_letter"
            android:textColor="@color/black"
            android:textSize="13dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/check1" />

        <CheckBox
            android:id="@+id/check3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="10dp"
            android:button="@drawable/checkbox"
            android:checked="false"
            android:clickable="false"
            android:focusable="false"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:paddingStart="6dp"
            android:text="@string/a_number"
            android:textColor="@color/black"
            android:textSize="13dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/check2" />


        <TextView
            android:id="@+id/textView17"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/confirm_password"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/check3" />

        <EditText
            android:id="@+id/confirmPassET"
            style="@style/mirrorText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_edit_corners"
            android:fontFamily="@font/poppins_medium_500"
            android:hint="@string/confirm_password"
            android:includeFontPadding="false"
            android:inputType="textPassword"
            android:padding="14dp"
            android:textColor="@color/black"
            android:textColorHint="@color/disable_color"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/textView17"
            app:layout_constraintTop_toBottomOf="@+id/textView17" />

        <ImageView
            android:id="@+id/showPassConfirmBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_eye_hide"
            app:layout_constraintBottom_toBottomOf="@+id/confirmPassET"
            app:layout_constraintEnd_toEndOf="@+id/confirmPassET"
            app:layout_constraintTop_toTopOf="@+id/confirmPassET" />

        <TextView
            android:id="@+id/updatePassBtn"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_gravity="bottom"
            android:layout_marginTop="60dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/bg_btn_round"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/update_password"
            android:textColor="@color/black"
            android:textSize="18dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/confirmPassET"
            app:layout_constraintVertical_bias="0.8"
            app:layout_constraintWidth_percent="0.6" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
