package feature.settings.profile

import domain.settings.profile.models.SettingsProfileDomainScope
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.core.component.getScopeId
import org.koin.dsl.module

val settingsProfileFeatureInjection = module {
    viewModel {
        val scope = getKoin().getOrCreateScope<SettingsProfileDomainScope>(SettingsProfileDomainScope.getScopeId())
        ChangeEmailViewModel(
            viewModel = scope.get(),
            handler = scope.get(),
            onClear = {  }
        )
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<SettingsProfileDomainScope>(SettingsProfileDomainScope.getScopeId())
        VerifyOtpViewModel(
            viewModel = scope.get(),
            handler = scope.get(),
            onClear = {  }
        )
    }
}