<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    android:id="@+id/profileParent"
    tools:context="feature.settings.company.staff.AddStaffActivity">

    <ImageView
        android:id="@+id/backBtnAddStaff"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="20dp"
        style="@style/ImageMirror"
        android:layout_marginTop="10dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/staffTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/addstaff"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtnAddStaff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtnAddStaff" />

    <androidx.core.widget.NestedScrollView
        android:layout_marginTop="10dp"
        android:layout_width="0dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtnAddStaff"
        android:layout_height="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="30dp">


            <TextView
                android:id="@+id/textView15"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="25dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/firstname"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/firstNameStaffET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:includeFontPadding="false"
                android:padding="14dp"
                android:layout_marginTop="4dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:inputType="text|textCapWords"
                android:textColor="@color/black"
                android:hint="@string/firstname"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView15"
                app:layout_constraintTop_toBottomOf="@+id/textView15" />

            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/last_name"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/firstNameStaffET" />

            <EditText

                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:layout_marginTop="4dp"
                android:inputType="text|textCapWords"
                android:textColor="@color/black"
                android:id="@+id/lastNameStaffET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:hint="@string/last_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView16"
                app:layout_constraintTop_toBottomOf="@+id/textView16" />

            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView17"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/user_name"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/lastNameStaffET" />

            <EditText
                android:id="@+id/userNameStaffET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:layout_marginTop="4dp"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:inputType="text|textCapWords"
                android:textColor="@color/black"
                android:hint="@string/user_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView17"
                app:layout_constraintTop_toBottomOf="@+id/textView17" />

            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView18"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/email"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/userNameStaffET" />

            <EditText
                android:id="@+id/emailProfileET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:layout_marginTop="4dp"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:textColor="@color/black"
                android:hint="@string/email"
                android:inputType="textEmailAddress"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView18"
                app:layout_constraintTop_toBottomOf="@+id/textView18" />

            <TextView
                android:layout_marginTop="26dp"
                android:id="@+id/textView19"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="Mobile Number"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/emailProfileET" />

            <EditText
                android:visibility="invisible"
                app:layout_constraintWidth_percent="0.2"
                app:layout_constraintBottom_toBottomOf="@+id/tvMobileNumber"
                app:layout_constraintEnd_toStartOf="@+id/tvMobileNumber"
                android:id="@+id/countryCodeET"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:text="+971"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:textColor="@color/black"
                android:gravity="center"
                android:includeFontPadding="false"
                android:inputType="number"
                app:layout_constraintStart_toStartOf="@+id/textView19"
                app:layout_constraintTop_toBottomOf="@+id/textView19">
            </EditText>

            <LinearLayout
                android:elevation="1dp"
                android:id="@+id/ll_country_code"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:background="@drawable/bg_edit_corners"
                android:orientation="horizontal"
                android:paddingStart="8dp"
                android:paddingTop="8dp"
                android:paddingEnd="12dp"
                android:paddingBottom="8dp"
                app:layout_constraintEnd_toEndOf="@+id/countryCodeET"
                app:layout_constraintBottom_toBottomOf="@+id/tvMobileNumber"
                app:layout_constraintStart_toStartOf="@+id/textView19"
                app:layout_constraintTop_toTopOf="@+id/tvMobileNumber">


                <com.mikhaellopez.circularimageview.CircularImageView
                    android:id="@+id/iv_flag_img"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="10dp"
                    app:civ_border="false"
                    tools:src="@drawable/flag_uae" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="8dp"
                    android:src="@drawable/arrow_black_down" />

            </LinearLayout>




            <EditText
                android:id="@+id/tvMobileNumber"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/enter_mobile_number"
                android:inputType="number"
                android:layout_marginTop="4dp"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:textSize="16dp"
                android:textColor="@color/black"
                app:layout_constraintEnd_toEndOf="@+id/emailProfileET"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/countryCodeET"
                app:layout_constraintTop_toTopOf="@+id/countryCodeET" />

            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView20"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/role"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvMobileNumber" />

            <EditText
                android:id="@+id/roleET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:layout_marginTop="4dp"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:inputType="text|textCapWords"
                android:textColor="@color/black"
                android:layout_marginEnd="20dp"
                android:cursorVisible="false"
                android:clickable="true"
                android:focusable="false"
                android:hint="@string/role"
                android:drawableEnd="@drawable/direction_bottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView20"
                app:layout_constraintTop_toBottomOf="@+id/textView20" />
            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView21"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:textColor="@color/black"
                android:textSize="16dp"
                android:text="@string/passportname"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/roleET" />

            <EditText
                android:id="@+id/passportNameET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:includeFontPadding="false"
                android:padding="14dp"
                android:layout_marginTop="4dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:inputType="text|textCapWords"
                android:textColor="@color/black"
                android:hint="@string/passportname"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView21"
                app:layout_constraintTop_toBottomOf="@+id/textView21" />



            <TextView
                style="@style/buttonStyle"
                android:layout_marginBottom="20dp"
                android:id="@+id/updateProfileBtn"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginTop="60dp"
                android:text="@string/btnSave"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/passportNameET"
                app:layout_constraintWidth_percent="0.6" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/fullGroup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="textView21,passportNameET,
                tvMobileNumber,
                textView19,textView17,userNameStaffET"/>


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>