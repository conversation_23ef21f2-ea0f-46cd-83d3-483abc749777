<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/mainLay"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:layout_marginEnd="10dp"
        android:id="@+id/staffNameTV"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/mirrorText"
        android:layout_marginStart="20dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:textColor="@color/black"
        app:layout_constraintEnd_toStartOf="@+id/isVarifiedTV"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_marginEnd="10dp"
        android:id="@+id/staffEmailTV"
        android:layout_width="0dp"
        style="@style/mirrorText"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:fontFamily="@font/poppins_regular_400"
        android:textColor="@color/black"
        app:layout_constraintEnd_toStartOf="@+id/memberTypeTV"
        app:layout_constraintStart_toStartOf="@+id/staffNameTV"
        app:layout_constraintTop_toBottomOf="@+id/staffNameTV" />

    <TextView
        android:id="@+id/staffMobTV"
        android:layout_marginTop="1dp"
        android:layout_width="0dp"
        style="@style/mirrorText"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_regular_400"
        android:textColor="@color/black"
        app:layout_constraintStart_toStartOf="@+id/staffEmailTV"
        app:layout_constraintTop_toBottomOf="@+id/staffEmailTV" />


    <TextView
        android:id="@+id/isVarifiedTV"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:fontFamily="@font/poppins_regular_400"
        android:text="@string/verified"
        android:textColor="@color/light_green"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@id/staffNameTV"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/memberTypeTV"
        android:layout_width="wrap_content"
        style="@style/mirrorText"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_regular_400"
        android:textColor="@color/black"
        app:layout_constraintBottom_toBottomOf="@id/staffEmailTV"
        app:layout_constraintEnd_toEndOf="@+id/isVarifiedTV"
        app:layout_constraintTop_toTopOf="@id/staffEmailTV" />

    <ImageView
        android:padding="5dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/dots"
        android:src="@drawable/option_dot_img"
        app:layout_constraintBottom_toBottomOf="@+id/staffMobTV"
        app:layout_constraintEnd_toEndOf="@+id/memberTypeTV"
        app:layout_constraintTop_toTopOf="@+id/staffMobTV" />

    <View
        android:id="@+id/view2"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="@color/line_bg_color"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="10dp"
        app:layout_constraintStart_toStartOf="@id/staffNameTV"
        app:layout_constraintTop_toBottomOf="@+id/staffMobTV" />


</androidx.constraintlayout.widget.ConstraintLayout>