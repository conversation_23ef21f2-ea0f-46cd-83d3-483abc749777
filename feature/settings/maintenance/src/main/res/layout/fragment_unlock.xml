<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
        tools:context="feature.pm.addlock.EnableBluetoothFragment">


    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/add_lock_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.038"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.073" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@mipmap/unlock_image"
        app:layout_constraintBottom_toTopOf="@+id/constraintLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout"
        android:layout_width="match_parent"
        android:layout_height="320dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="@+id/iv_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_lock_detected"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/your_lock_has_been_detected"
            android:textColor="@color/black"
            android:textSize="22dp"
            app:layout_constraintBottom_toTopOf="@+id/tv_lock_type"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tv_lock"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="30dp"
            android:fontFamily="@font/poppins_regular_400"
            android:text="@string/lock_type"
            android:textColor="@color/black"
            android:textSize="18dp"
            app:layout_constraintBottom_toTopOf="@+id/tv_verify"
            app:layout_constraintStart_toStartOf="@+id/tv_lock_detected" />

        <TextView
            android:id="@+id/tv_lock_type"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:layout_marginStart="4dp"
            android:fontFamily="@font/poppins_bold_700"
            android:textColor="@color/black"
            android:textSize="18dp"
            style="@style/mirrorText"
            app:layout_constraintBottom_toBottomOf="@+id/tv_lock"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_lock"
            app:layout_constraintTop_toTopOf="@+id/tv_lock" />

        <TextView
            android:id="@+id/tv_verify"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_medium_500"
            android:text="@string/unlock_to_verify"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintBottom_toTopOf="@+id/tv_press"
            app:layout_constraintEnd_toEndOf="@+id/tv_lock_type"
            app:layout_constraintStart_toStartOf="@+id/tv_lock" />

        <TextView
            android:id="@+id/tv_press"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="30dp"
            android:fontFamily="@font/poppins_regular_400"
            android:text="@string/press_button_to_unlock_your_lock"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintBottom_toTopOf="@+id/btn_unlock"
            app:layout_constraintEnd_toEndOf="@+id/tv_lock_type"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="@+id/tv_verify" />

        <TextView
            android:id="@+id/btn_unlock"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="60dp"
            android:layout_marginEnd="60dp"
            android:layout_marginBottom="60dp"
            android:background="@drawable/bg_btn_round"
            android:fontFamily="@font/poppins_regular_400"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/unlock"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/progress_lay"
        android:id="@+id/progressLay"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>