package feature.settings.support.nearby

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import core.permissions.manager.AndroidBluetoothManager
import core.permissions.manager.AndroidPermissionsManager
import core.permissions.manager.BluetoothManager
import core.permissions.manager.PermissionsManager
import domain.settings.support.models.ScreenEvent
import domain.settings.support.models.ScreenState
import keyless.feature.settings.support.databinding.ActivityNearByDeviceBinding
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.android.ext.android.inject

class NearByDeviceActivity : AppCompatActivity() {

    private val horizontalLayoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
    private lateinit var adapter: NearByLockAdapter
    private lateinit var binding: ActivityNearByDeviceBinding
    private lateinit var viewModel: NearByDeviceViewModel
    private val bluetooth: BluetoothManager by inject()
    private val permissions: PermissionsManager by inject()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        (permissions as? AndroidPermissionsManager)?.bind(this)
        (bluetooth as? AndroidBluetoothManager)?.bind(this)
        binding = ActivityNearByDeviceBinding.inflate(layoutInflater)
        viewModel = ViewModelProvider(this)[NearByDeviceViewModel::class.java]

        viewModel.onEvent(ScreenEvent.InitNearBy)

        setContentView(binding.root)
        initViews()
        setAdapter()
        collectState()
    }

    private fun setAdapter() {
        binding.deviceRV.layoutManager = horizontalLayoutManager
        horizontalLayoutManager.reverseLayout = false
        binding.deviceRV.itemAnimator = null

        adapter = NearByLockAdapter(this)
        binding.deviceRV.adapter = adapter
    }

    private fun collectState() {
        viewModel
            .screenState
            .onEach {
                val state = (it.state as? ScreenState.State.Nearby) ?: return@onEach

                updateUI(state)

                adapter.addNewItems(state.devices)
            }
            .launchIn(lifecycleScope)
    }

    override fun onDestroy() {
        viewModel.onEvent(ScreenEvent.EndNearBy)
        super.onDestroy()
    }

    override fun onResume() {
        viewModel.onEvent(ScreenEvent.ResumeNearBy)
        super.onResume()
    }

    override fun onPause() {
        viewModel.onEvent(ScreenEvent.PauseNearBy)
        super.onPause()
    }

    private fun updateUI(state: ScreenState.State.Nearby) {
        binding.refreshBtn.visibility = state.showRefresh.toggleGone()
        binding.noData.visibility = state.devices.isEmpty().toggleGone()
        binding.deviceProgressBar.visibility = state.devices.isEmpty().toggleGone()
    }

    private fun initViews() {
        binding.refreshBtn.setOnClickListener {
            viewModel.onEvent(ScreenEvent.ResumeNearBy)
        }

        binding.backBtnDevice.setOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }
    }

    private fun Boolean.toggleGone(): Int {
        return if (this) View.VISIBLE else View.GONE
    }
}