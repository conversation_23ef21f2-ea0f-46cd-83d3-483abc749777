<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent">


    <ImageView
        android:id="@+id/backBtn"
        style="@style/ImageMirror"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:padding="20dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/titleToolbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/select_lock"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtn" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"

        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtn"
        app:layout_constraintVertical_bias="0.0">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/lockListView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0">


                <EditText
                    android:id="@+id/svLock"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginEnd="20dp"
                    style="@style/mirrorText"
                    android:background="@drawable/bg_btn_round"
                    android:backgroundTint="@color/bg_edit_grey"
                    android:drawableStart="@drawable/ic_baseline_search_24"
                    android:drawablePadding="10dp"
                    android:ellipsize="end"
                    android:focusableInTouchMode="true"
                    android:fontFamily="@font/poppins_regular_400"
                    android:hint="@string/search_by_lock_name"
                    android:includeFontPadding="false"
                    android:padding="8dp"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:paddingRight="30dp"
                    android:singleLine="true"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/stopSearch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:src="@drawable/iv_cross_black"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@+id/svLock"
                    app:layout_constraintEnd_toEndOf="@+id/svLock"
                    app:layout_constraintTop_toTopOf="@+id/svLock" />


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvRayonicLocks"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/svLock" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/selectLockView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/white_top_corners"
                android:visibility="gone"
                android:layout_marginBottom="16dp"
                app:layout_constraintBottom_toTopOf="@+id/btnAddCard"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0">


                <TextView
                    android:id="@+id/tv_brand"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/poppins_regular_400"
                    android:text="@string/selected_lock"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <EditText
                    android:id="@+id/etSelectedLock"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="20dp"
                    android:alpha="0.5"
                    style="@style/mirrorText"
                    android:background="@drawable/bg_edit_corners"
                    android:editable="false"
                    android:fontFamily="@font/poppins_medium_500"
                    android:hint="@string/selected_lock"
                    android:includeFontPadding="false"
                    android:padding="14dp"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_brand"
                    tools:ignore="Deprecated" />


                <TextView
                    android:id="@+id/tvCardId"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/poppins_regular_400"
                    android:text="@string/card_to_be_configured"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/etSelectedLock" />

                <EditText
                    android:id="@+id/etCardId"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="8dp"
                    style="@style/mirrorText"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/bg_edit_corners"
                    android:drawableEnd="@drawable/arrow_black_down"
                    android:editable="false"
                    android:focusable="false"
                    android:fontFamily="@font/poppins_medium_500"
                    android:hint="@string/card_id"
                    android:includeFontPadding="false"
                    android:padding="14dp"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvCardId"
                    tools:ignore="Deprecated" />

                <TextView
                    android:id="@+id/tvEmail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/poppins_regular_400"
                    android:text="@string/email"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/etCardId" />


                <LinearLayout
                    android:id="@+id/linearResults"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginBottom="32dp"
                    android:orientation="vertical"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvEmail"
                    app:layout_constraintVertical_bias="0.0">

                    <TextView
                        android:id="@+id/tvUserName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_regular_400"
                        android:text="@string/user_name"
                        android:textColor="@color/black"
                        android:textSize="16dp"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/etCardId" />

                    <EditText
                        android:id="@+id/etUserName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        style="@style/mirrorText"
                        android:background="@drawable/bg_edit_corners"
                        android:fontFamily="@font/poppins_medium_500"
                        android:hint="@string/user_name"
                        android:includeFontPadding="false"
                        android:padding="14dp"
                        android:inputType="text"
                        android:imeOptions="actionNext"
                        android:textColor="@color/black"
                        android:textSize="16dp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvUserName"
                        tools:ignore="Deprecated" />


                    <TextView
                        android:id="@+id/tvMobileNumber"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:fontFamily="@font/poppins_regular_400"
                        android:text="@string/mobile_number"
                        android:textColor="@color/black"
                        android:textSize="16dp"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/etUserName" />

                    <EditText
                        android:id="@+id/etMobileNumber"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:background="@drawable/bg_edit_corners"
                        android:fontFamily="@font/poppins_medium_500"
                        android:hint="@string/mobile_number"
                        android:includeFontPadding="false"
                        android:padding="14dp"
                        style="@style/mirrorText"
                        android:inputType="phone"
                        android:imeOptions="actionNext"
                        android:textColor="@color/black"
                        android:textSize="16dp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvEmail"
                        tools:ignore="Deprecated" />


                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/userDetails"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="4dp"
                        android:layout_marginBottom="8dp"
                        android:theme="@style/Theme.MaterialComponents.Light"
                        android:visibility="gone"
                        card_view:cardBackgroundColor="@color/card_bg_color"
                        card_view:cardCornerRadius="10dp"
                        card_view:cardElevation="0dp"
                        card_view:cardMaxElevation="0dp"
                        card_view:strokeColor="@color/card_stroke_access"
                        card_view:strokeWidth="1dp">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">


                            <TextView
                                android:id="@+id/txtName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="16dp"
                                android:layout_marginTop="16dp"
                                android:fontFamily="@font/poppins_semibold_600"
                                android:includeFontPadding="false"
                                android:textColor="@color/black"
                                android:textSize="14dp"
                                card_view:layout_constraintStart_toStartOf="parent"
                                card_view:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/txtEmail"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="16dp"
                                android:layout_marginTop="4dp"
                                android:layout_marginBottom="16dp"
                                android:fontFamily="@font/poppins_regular_400"
                                android:includeFontPadding="false"
                                android:textColor="@color/black"
                                android:textSize="14dp"
                                android:visibility="visible"
                                card_view:layout_constraintBottom_toBottomOf="parent"
                                card_view:layout_constraintStart_toStartOf="parent"
                                card_view:layout_constraintTop_toBottomOf="@+id/txtName"
                                card_view:layout_constraintVertical_bias="0.0" />

                            <TextView
                                android:id="@+id/txtPhone"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="16dp"
                                android:layout_marginTop="4dp"
                                android:layout_marginBottom="16dp"
                                android:fontFamily="@font/poppins_regular_400"
                                android:includeFontPadding="false"
                                android:textColor="@color/black"
                                android:textSize="14dp"
                                android:visibility="visible"
                                card_view:layout_constraintBottom_toBottomOf="parent"
                                card_view:layout_constraintStart_toStartOf="parent"
                                card_view:layout_constraintTop_toBottomOf="@+id/txtEmail"
                                card_view:layout_constraintVertical_bias="0.0" />


                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>


                </LinearLayout>


                <TextView
                    android:id="@+id/tvStartTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:fontFamily="@font/poppins_regular_400"
                    android:text="@string/start_time"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:layout_marginTop="15dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/linearResults" />

                <EditText
                    android:id="@+id/etStartTime"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/bg_edit_corners"
                    android:clickable="true"
                    android:editable="true"
                    android:focusable="false"
                    android:fontFamily="@font/poppins_medium_500"
                    android:hint="@string/start_time"
                    android:includeFontPadding="false"
                    android:padding="14dp"
                    style="@style/mirrorText"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvStartTime"
                    tools:ignore="Deprecated" />

                <TextView
                    android:id="@+id/tvEndTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/poppins_regular_400"
                    android:text="@string/end_time"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/etStartTime" />

                <EditText
                    android:id="@+id/etEndTime"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginBottom="24dp"
                    android:background="@drawable/bg_edit_corners"
                    android:clickable="true"
                    android:editable="true"
                    android:focusable="false"
                    android:fontFamily="@font/poppins_medium_500"
                    android:hint="@string/end_time"
                    android:includeFontPadding="false"
                    android:padding="14dp"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:visibility="gone"
                    style="@style/mirrorText"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvEndTime"
                    app:layout_constraintVertical_bias="0.0"
                    tools:ignore="Deprecated" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <TextView
                android:id="@+id/btnAddCard"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginStart="60dp"
                android:layout_marginEnd="60dp"
                android:layout_marginBottom="32dp"
                android:background="@drawable/bg_btn_round"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:text="@string/configure_card"
                android:textColor="@color/black"
                android:textSize="16dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.core.widget.NestedScrollView>


    <!--    <ProgressBar-->
    <!--        android:id="@+id/progress_pagination"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginBottom="12dp"-->
    <!--        android:theme="@style/progressBarBlue"-->
    <!--        android:visibility="gone"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintStart_toStartOf="parent" />-->


    <LinearLayout
        android:id="@+id/layNoDataFullPage"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/iv_no_locks" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/no_locks_assigned"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rv_lock" />
    </LinearLayout>


    <include
        android:id="@+id/progressBar"
        layout="@layout/progress_lay"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>