plugins {
    id("keyless.android.feature")
}

android {
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":core-lock-common"))
    implementation(project(":core-permissions-manager"))
    implementation(project(":data-common"))
    implementation(project(":data-error"))
    implementation(project(":data-company"))
    implementation(project(":data-user-home"))
    implementation(project(":domain-common"))
    implementation(project(":domain-settings-support"))
    implementation(project(":feature-common"))
}