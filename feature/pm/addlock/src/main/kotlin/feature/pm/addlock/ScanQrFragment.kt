package feature.pm.addlock

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import data.network.android.models.ModelAdminInstaller
import data.utils.android.CommonValues
import keyless.feature.pm.addlock.R
import keyless.feature.pm.addlock.databinding.FragmentScanQrBinding

class ScanQrFragment : Fragment() {

    private lateinit var binding: FragmentScanQrBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentScanQrBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setOnClickEvents(view)
    }

    private fun setOnClickEvents(view: View) {
        val argumentPlace = arguments?.getString("whichPlace").toString()
        val argumentList = arguments?.getParcelable<ModelAdminInstaller.DataModelInstaller>("list")
        val bundle = Bundle()
        bundle.putString("whichPlace", argumentPlace)
        bundle.putParcelable("list", argumentList)
        binding.btnNext.setOnClickListener {
            CommonValues.loadFragment(
                ScanFragment(),
                bundle,
                parentFragmentManager,
                R.id.frameContainerAddLock
            )
        }
    }
}