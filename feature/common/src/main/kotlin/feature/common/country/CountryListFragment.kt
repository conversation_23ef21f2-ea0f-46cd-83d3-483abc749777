package feature.common.country

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hbb20.CCPCountry
import data.utils.android.hideKeyboard
import keyless.feature.common.R
import org.greenrobot.eventbus.EventBus

class CountryListFragment : Fragment() {

    lateinit var countryAdapter: CountryAdapter
    lateinit var onBackCountry: ImageView
    lateinit var stopSearch: ImageView
    lateinit var countryRV: RecyclerView
    lateinit var searchCountryET: EditText
//    private lateinit var binding : CountryListFragmentBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.country_list_fragment, container, false)
        onBackCountry = view.findViewById(R.id.onBackCountry)
        stopSearch = view.findViewById(R.id.stopSearch)
        countryRV = view.findViewById(R.id.countryRV)
        searchCountryET = view.findViewById(R.id.searchCountryET)

        return view
//        binding = CountryListFragmentBinding.inflate(layoutInflater)
//        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showCountryList()
        searchCountry()
        onClick()
    }

    private fun onClick() {
        onBackCountry.setOnClickListener {
            requireActivity().supportFragmentManager.popBackStack()
        }
        countryAdapter.onClick = {
            EventBus.getDefault().post(it)
            requireActivity().supportFragmentManager.popBackStack()
        }

        stopSearch.setOnClickListener {
            hideKeyboard()
            searchCountryET.setText("")
            countryAdapter.filter.filter("")
        }
    }

    private fun searchCountry() {
        searchCountryET.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                try {
                    if (p0.toString().isNotEmpty()) {
                        countryAdapter.filter.filter(p0.toString())
                        stopSearch.isVisible = true
                    } else {
                        stopSearch.isVisible = false
                    }
                } catch (e: Exception) {
                    Log.e("exception", e.printStackTrace().toString())
                    e.printStackTrace()
                }
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })
    }

    private fun showCountryList() {
        try {
            val materList = CCPCountry.getLibraryMasterCountriesEnglish()
            countryAdapter = CountryAdapter()
            val itemDecorator = DividerItemDecoration(context, DividerItemDecoration.VERTICAL)
            itemDecorator.setDrawable(
                ContextCompat.getDrawable(
                    requireActivity(),
                    keyless.data.utils.android.R.drawable.country_divider
                )!!
            )
            countryRV.recycledViewPool.clear()
            countryRV.layoutManager = LinearLayoutManager(requireActivity())
            countryRV.addItemDecoration(itemDecorator)
            countryRV.itemAnimator = null
            countryRV.adapter = countryAdapter
            countryAdapter.addData(materList)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}