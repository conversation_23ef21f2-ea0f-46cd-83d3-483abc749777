package feature.common.country

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import androidx.recyclerview.widget.RecyclerView
import com.hbb20.CCPCountry
import data.utils.android.CommonValues
import feature.common.countries.Countries.getFlag
import keyless.feature.common.databinding.CountryAdapterLayoutBinding

class CountryAdapter :
    RecyclerView.Adapter<CountryAdapter.OnViewHolder>(),
    Filterable {
    lateinit var context: Context
    var listNew: ArrayList<CCPCountry>? = null
    var listFiltered: ArrayList<CCPCountry>? = null
    var onClick: ((CCPCountry) -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OnViewHolder {
        context = parent.context
        val binding = CountryAdapterLayoutBinding.inflate(LayoutInflater.from(context), parent, false)
        return OnViewHolder(binding)
    }

    inner class OnViewHolder(val binding: CountryAdapterLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            itemView.setOnClickListener {
                onClick?.invoke(listFiltered!![adapterPosition])
                val item = listFiltered!![adapterPosition]
                CommonValues.itemForFlag = item.phoneCode
                CommonValues.itemForCountryName = item.nameCode
                Log.e("cc111 " + CommonValues.itemForFlag, "")
                Log.e("cc112 " + CommonValues.itemForCountryName, "")
            }
        }
        fun bind(item: CCPCountry) {
            binding.countryImg.setImageResource(getFlag(item))
            binding.countryName.text = item.name.trim()
            binding.countryCode.text = "+" + item.phoneCode
        }
    }

    override fun onBindViewHolder(holder: OnViewHolder, position: Int) {
//        val item = listFiltered[position]
        holder.bind(listFiltered!![position])
    }

    override fun getItemCount(): Int = listFiltered?.size ?: 0
    fun addData(list: List<CCPCountry>) {
        var listNew1 = list as ArrayList<CCPCountry>
        listNew = ArrayList()
        listNew!!.clear()
        var temp = listNew1.sortedBy { it.name.toString() }.toArrayList()
        listNew!!.addAll(temp)
        listFiltered = listNew
        notifyDataSetChanged()
    }

    fun <T> List<T>.toArrayList(): ArrayList<T> {
        return ArrayList(this)
    }

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                val charString = constraint?.toString() ?: ""
                try {
                    listFiltered = if (charString.isEmpty()) {
                        listNew
                    } else {
                        val mFilteredList = ArrayList<CCPCountry>()
                        listNew?.filter {
                            (it.name.contains(constraint!!, true)) or
                                (it.name.startsWith(constraint, true))
                        }
                            ?.forEach { mFilteredList.add(it) }
                        mFilteredList
                    }
                } catch (e: Exception) {
                    Log.e("exception", "")
                    e.printStackTrace()
                }
                return FilterResults().apply { values = listFiltered }
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                try {
                    listFiltered = if (results?.values == null) {
                        ArrayList()
                    } else {
                        results.values as ArrayList<CCPCountry>
                    }
                    notifyDataSetChanged()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }
}