package feature.common.compose.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontSynthesis
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp

internal val Poppins = FontFamily(
    Font(keyless.data.utils.android.R.font.poppins_regular_400, weight = FontWeight.Normal),
    Font(keyless.data.utils.android.R.font.poppins_medium_500, weight = FontWeight.Medium),
    Font(keyless.data.utils.android.R.font.poppins_semibold_600, weight = FontWeight.SemiBold),
    Font(keyless.data.utils.android.R.font.poppins_bold_700, weight = FontWeight.Bold)
)

val mainFontFamily = Poppins

internal fun extendedTypography(): Typography {
    val default = Typography()

    return Typography(
        displayLarge = default.displayLarge.copy(
            fontFamily = mainFontFamily
        ),
        displayMedium = default.displayMedium.copy(
            fontFamily = mainFontFamily
        ),
        displaySmall = default.displaySmall.copy(
            fontFamily = mainFontFamily
        ),
        headlineLarge = default.headlineLarge.copy(
            fontFamily = mainFontFamily
        ),
        headlineMedium = default.headlineMedium.copy(
            fontFamily = mainFontFamily
        ),
        headlineSmall = default.headlineSmall.copy(
            fontFamily = mainFontFamily
        ),
        titleLarge = default.titleLarge.copy(
            fontFamily = mainFontFamily,
            fontSize = 24.sp
        ),
        titleMedium = default.titleMedium.copy(
            fontFamily = mainFontFamily,
            fontSize = 20.sp
        ),
        titleSmall = default.titleSmall.copy(
            fontFamily = mainFontFamily
        ),
        bodyLarge = default.bodyLarge.copy(
            fontFamily = mainFontFamily
        ),
        bodyMedium = default.bodyMedium.copy(
            fontFamily = mainFontFamily,
            fontWeight = FontWeight.Medium,
            fontSize = 16.sp
        ),
        bodySmall = default.bodySmall.copy(
            fontFamily = mainFontFamily
        ),
        labelLarge = default.labelLarge.copy(
            fontFamily = mainFontFamily
        ),
        labelMedium = default.labelMedium.copy(
            fontFamily = mainFontFamily,
            fontSynthesis = FontSynthesis.All
        ),
        labelSmall = default.labelSmall.copy(
            fontFamily = mainFontFamily,
        ),
    )
}