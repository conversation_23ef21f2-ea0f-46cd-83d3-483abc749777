<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:padding="10dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="13dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/white_all_corners_10"
        android:paddingBottom="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <TextView
            android:id="@+id/ivHeader"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="8dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/alert"
            android:textColor="@color/black"
            android:textSize="20sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTextA"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/poppins_regular_400"
            android:paddingHorizontal="12dp"
            android:textAlignment="center"
            android:textColor="@android:color/black"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivHeader" />

        <TextView
            android:id="@+id/tvSubmitA"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginStart="28dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/no"
            android:textAlignment="center"
            android:textColor="@color/colorAccent"
            android:textSize="20sp"
            app:layout_constraintStart_toStartOf="@+id/tvTextA"
            app:layout_constraintTop_toBottomOf="@+id/tvTextA" />

        <TextView
            android:id="@+id/tvCancel"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/yes"
            android:layout_marginEnd="28dp"
            android:textAlignment="center"
            android:textColor="@color/colorAccent"
            android:textSize="20sp"
            app:layout_constraintEnd_toEndOf="@+id/tvTextA"
            app:layout_constraintTop_toTopOf="@+id/tvSubmitA" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>