<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:id="@+id/mainLay"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardView"
        android:theme="@style/Theme.MaterialComponents.Light"
        android:layout_width="match_parent"
        android:layout_height="92dp"
        card_view:cardCornerRadius="10dp"
        card_view:cardElevation="0dp"
        android:layout_marginBottom="8dp"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        card_view:cardMaxElevation="0dp"
        card_view:strokeColor="@color/border_color"
        card_view:strokeWidth="1dp"
        card_view:layout_constraintEnd_toEndOf="parent"
        card_view:layout_constraintStart_toStartOf="parent"
        card_view:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivIcons"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center" />

    </com.google.android.material.card.MaterialCardView>


    <TextView
        android:id="@+id/txtIconName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:ellipsize="end"
        android:layout_marginBottom="14dp"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxLines="2"
        android:text="@string/living_room_lock"
        android:textColor="@color/black"
        android:textSize="12dp"
        card_view:layout_constraintBottom_toBottomOf="parent"
        card_view:layout_constraintEnd_toEndOf="parent"
        card_view:layout_constraintStart_toStartOf="parent"
        card_view:layout_constraintTop_toBottomOf="@+id/cardView"
        card_view:layout_constraintVertical_bias="0.0" />
</androidx.constraintlayout.widget.ConstraintLayout>