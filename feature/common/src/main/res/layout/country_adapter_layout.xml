<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.mikhaellopez.circularimageview.CircularImageView
        android:layout_marginTop="7dp"
        android:layout_marginBottom="7dp"
        android:layout_marginStart="20dp"
        android:src="@drawable/flag_uae"
        app:civ_border="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:id="@+id/countryImg"/>

    <TextView
        android:ellipsize="end"
        android:layout_marginEnd="20dp"
        android:maxLines="1"
        android:id="@+id/countryName"
        style="@style/mirrorText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:fontFamily="@font/poppins_regular_400"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="@+id/countryImg"
        app:layout_constraintEnd_toStartOf="@+id/countryCode"
        app:layout_constraintStart_toEndOf="@+id/countryImg"
        app:layout_constraintTop_toTopOf="@+id/countryImg" />

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:text="@string/_91"
        android:id="@+id/countryCode"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="@+id/countryName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/countryName" />



</androidx.constraintlayout.widget.ConstraintLayout>