<?xml version="1.0" encoding="utf-8"?>
<resources tools:shrinkMode="strict" xmlns:tools="http://schemas.android.com/tools">

    <style name="Theme.Transparent" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:colorBackground">@color/white</item>
        <item name="android:statusBarColor">@color/colorAccent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowSplashScreenAnimatedIcon">@android:color/transparent</item>
        <item name="android:windowSplashScreenAnimationDuration">0</item>
        <item name="android:windowSplashScreenBackground">@color/transparent</item>
        <item name="android:windowBackground">@color/white</item>


        <!--        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>-->
<!--        <item name="android:windowTranslucentStatus">true</item>-->
<!--        <item name="android:windowTranslucentNavigation">true</item>-->
<!--        <item name="android:windowFullscreen">true</item>-->

    </style>
</resources>