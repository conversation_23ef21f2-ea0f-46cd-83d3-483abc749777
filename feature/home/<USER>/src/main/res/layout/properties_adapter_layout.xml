<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:id="@+id/mainLay"
    android:layout_height="wrap_content">


    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/Theme.MaterialComponents.Light"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        card_view:cardCornerRadius="10dp"
        android:layout_marginBottom="8dp"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        card_view:cardElevation="0dp"
        card_view:cardMaxElevation="0dp"
        card_view:cardUseCompatPadding="true"
        card_view:strokeColor="@color/border_color"
        card_view:strokeWidth="1dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/ivPropertyIcon"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_marginStart="20dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/txtLocation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/living_room_lock"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toEndOf="@+id/ivPropertyIcon"
                app:layout_constraintTop_toTopOf="@+id/ivPropertyIcon" />

            <TextView
                android:id="@+id/txtDeviceCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="center"
                android:includeFontPadding="false"
                android:textColor="@color/black"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/ivPropertyIcon"
                app:layout_constraintStart_toStartOf="@+id/txtLocation"
                app:layout_constraintTop_toBottomOf="@+id/txtLocation" />

            <ImageView
                android:id="@+id/icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="30dp"
                android:background="@drawable/ic_arrow_forward"
                app:layout_constraintBottom_toBottomOf="@+id/ivPropertyIcon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ivPropertyIcon" />

            <View
                android:id="@+id/view"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="15dp"
                android:background="#F1F1F1"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/txtDeviceCount" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvPropertiesDetail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="12dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>
</androidx.constraintlayout.widget.ConstraintLayout>