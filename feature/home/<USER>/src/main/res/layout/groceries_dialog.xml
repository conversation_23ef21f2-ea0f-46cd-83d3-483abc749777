<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    card_view:cardCornerRadius="10dp"
    card_view:cardElevation="4dp"
    android:layout_gravity="center">


    <androidx.cardview.widget.CardView
        android:id="@+id/cardView2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="15dp"
        android:layout_gravity="center"
        card_view:cardCornerRadius="10dp"
        card_view:cardElevation="0dp"
        card_view:cardUseCompatPadding="true"
        card_view:layout_constraintBottom_toBottomOf="parent"
        card_view:layout_constraintEnd_toEndOf="parent"
        card_view:layout_constraintStart_toStartOf="parent"
        card_view:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_groceries"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="20dp"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:text="@string/groceries"
                android:textColor="@color/black"
                android:textSize="18sp" />

            <androidx.cardview.widget.CardView
                android:id="@+id/cv_instashop"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="13dp"
                android:layout_marginEnd="13dp"
                card_view:cardCornerRadius="10dp"
                card_view:cardElevation="6dp"
                card_view:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/iv_instashop"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="10dp"
                        android:background="@drawable/ic_insta_shop_image" />

                    <TextView
                        android:id="@+id/tv_instashop"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_medium_500"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:text="@string/instashop"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cv_noon_daily"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="13dp"
                android:layout_marginEnd="13dp"
                android:layout_marginBottom="20dp"
                card_view:cardCornerRadius="10dp"
                card_view:cardElevation="6dp"
                card_view:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/iv_noon_daily"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="10dp"
                        android:background="@drawable/ic_noon_daily_image" />

                    <TextView
                        android:id="@+id/tv_noon_daily"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_medium_500"
                        android:gravity="center"
                        android:text="@string/noon_daily"
                        android:includeFontPadding="false"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <ImageView
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:background="@drawable/iv_cross_bg_white"
        android:elevation="50dp"
        android:id="@+id/closeDialog"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="4dp"
        android:src="@drawable/iv_cross_black"
        card_view:layout_constraintBottom_toBottomOf="@+id/cardView2"
        card_view:layout_constraintEnd_toEndOf="parent"
        card_view:layout_constraintHorizontal_bias="1.0"
        card_view:layout_constraintStart_toStartOf="parent"
        card_view:layout_constraintTop_toTopOf="parent"
        card_view:layout_constraintVertical_bias="0.0" />

</androidx.constraintlayout.widget.ConstraintLayout>
