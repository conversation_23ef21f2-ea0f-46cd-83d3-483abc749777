<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    tools:context=".dashboard.home.HomeFragment">


    <ImageView
        android:id="@+id/backBtn"
        style="@style/ImageMirror"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:padding="20dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/titleToolbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/select_lock"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtn" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="15dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtn"
        app:layout_constraintVertical_bias="0.0">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="visible"
            android:id="@+id/screenSelectLock"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <TextView
                android:id="@+id/textView32"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/select_the_rayonics_lock_to_be_used_for_adding_cards"
                android:textColor="@color/black"
                android:textSize="18dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <EditText
                android:id="@+id/svLockAdmin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="20dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_btn_round"
                android:backgroundTint="@color/bg_edit_grey"
                android:drawableStart="@drawable/ic_baseline_search_24"
                android:drawablePadding="10dp"
                android:ellipsize="end"
                android:focusableInTouchMode="true"
                android:fontFamily="@font/poppins_regular_400"
                android:hint="@string/search_by_internal_id_or_lock_id"
                android:imeOptions="actionSearch"
                android:includeFontPadding="false"
                android:padding="8dp"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:paddingRight="30dp"
                android:singleLine="true"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView32" />

            <ImageView
                android:id="@+id/stopSearchAdmin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="16dp"
                android:src="@drawable/iv_cross_black"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/svLockAdmin"
                app:layout_constraintEnd_toEndOf="@+id/svLockAdmin"
                app:layout_constraintTop_toTopOf="@+id/svLockAdmin" />


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvAdminLocks"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/svLockAdmin"
                app:layout_constraintVertical_bias="0.0" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="gone"
            android:id="@+id/screenAddRayonicsCard"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0">

            <TextView
                android:id="@+id/tv_brand"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/next_card_id_to_be_generated"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/etInternalId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="20dp"
                android:background="@drawable/bg_edit_corners"
                android:editable="false"
                style="@style/mirrorText"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/next_card_id_to_be_generated"
                android:includeFontPadding="false"
                android:padding="14dp"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_brand"
                tools:ignore="Deprecated" />


            <TextView
                android:id="@+id/textView34"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/tap_the_card"
                android:textColor="@color/black"
                android:textSize="17dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etInternalId" />


            <TextView
                android:id="@+id/tvCardId"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/card_id"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView34" />

            <EditText
                android:id="@+id/etCardId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="20dp"
                android:background="@drawable/bg_edit_corners"
                android:editable="false"
                style="@style/mirrorText"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/card_id"
                android:includeFontPadding="false"
                android:focusable="false"
                android:padding="14dp"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvCardId"
                tools:ignore="Deprecated" />


            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="14dp"
                android:src="@drawable/iv_scan_add_new"
                app:layout_constraintBottom_toBottomOf="@+id/etCardId"
                app:layout_constraintEnd_toEndOf="@+id/etCardId"
                app:layout_constraintTop_toTopOf="@+id/etCardId" />



            <TextView
                android:id="@+id/btnAddCard"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginStart="60dp"
                android:layout_marginTop="43dp"
                android:layout_marginEnd="60dp"
                android:layout_marginBottom="41dp"
                android:background="@drawable/bg_btn_round"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:text="@string/add_card"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />



        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>


    <ProgressBar
        android:id="@+id/progress_pagination"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:theme="@style/progressBarBlue"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


    <LinearLayout
        android:id="@+id/layNoDataFullPage"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/iv_no_locks" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/no_locks_are_found"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rv_lock" />
    </LinearLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:background="#33000000"
        android:id="@+id/progressBar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        android:layout_height="match_parent">


        <LinearLayout
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:gravity="center"
            android:background="@drawable/white_all_corners_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_gravity="center"
                android:theme="@style/progressBarBlue"
                android:progressTint="@color/colorAccent"
                android:progressBackgroundTint="@color/colorAccent"
                android:layout_height="wrap_content"/>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>




</androidx.constraintlayout.widget.ConstraintLayout>