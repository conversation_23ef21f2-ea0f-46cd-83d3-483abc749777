<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView android:theme="@style/Theme.MaterialComponents.Light"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    card_view:cardCornerRadius="10dp"
    card_view:cardElevation="0dp"
    android:layout_marginBottom="8dp"
    android:id="@+id/lay"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    card_view:cardMaxElevation="0dp"
    card_view:cardUseCompatPadding="true"
    card_view:strokeColor="@color/border_color"
    card_view:strokeWidth="1dp"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="90dp"
            android:layout_height="70dp"
            android:visibility="visible"
            android:id="@+id/inactive"
            style="@style/ImageMirror"
            android:background="@drawable/banner"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toTopOf="parent"
            card_view:layout_constraintVertical_bias="0.0">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:rotation="-35"
                android:text="@string/inactive"
                android:textColor="@color/white"
                android:textSize="10dp"
                android:translationX="-10dp"
                android:translationY="-3dp"
                android:fontFamily="@font/poppins_regular_400"
                card_view:layout_constraintBottom_toBottomOf="parent"
                card_view:layout_constraintEnd_toEndOf="parent"
                card_view:layout_constraintStart_toStartOf="parent"
                card_view:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>



        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="vertical"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ivIconHome"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginTop="25dp" />

            <TextView
                android:id="@+id/txtLockName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="14dp"
                android:layout_marginEnd="10dp"
                android:ellipsize="end"
                android:fontFamily="@font/poppins_semibold_600"
                android:gravity="center"
                android:includeFontPadding="false"
                android:maxLines="2"
                android:textColor="@color/black"
                android:textSize="16dp" />

            <TextView
                android:id="@+id/txtLockPlace"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="10dp"
                android:ellipsize="end"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="center"
                android:includeFontPadding="false"
                android:maxLines="2"
                android:textColor="@color/black"
                android:textSize="14dp" />


            <TextView
                android:id="@+id/txtPropertyName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="10dp"
                android:ellipsize="end"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="center"
                android:includeFontPadding="false"
                android:maxLines="2"
                android:textColor="@color/black"
                android:textSize="14dp" />
        </LinearLayout>



    </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>
