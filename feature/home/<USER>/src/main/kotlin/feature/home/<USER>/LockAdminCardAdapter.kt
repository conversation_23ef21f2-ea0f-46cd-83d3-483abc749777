package feature.home.admin

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import androidx.annotation.RequiresApi
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import data.network.android.LockModelAdmin
import data.utils.android.CommonValues
import keyless.feature.home.admin.databinding.LockAdapterAdminLayoutBinding

class LockAdminCardAdapter(context: Context) :
    RecyclerView.Adapter<LockAdminCardAdapter.ViewHolder>(), Filterable {

    var listener = context as ClickToConnect
    lateinit var contxt: Context
    var adminLockData: ArrayList<LockModelAdmin> = ArrayList()
    var listFiltered: ArrayList<LockModelAdmin> = ArrayList()

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        contxt = viewGroup.context
        val binding = LockAdapterAdminLayoutBinding.inflate(
            LayoutInflater.from(contxt),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    inner class ViewHolder(val binding: LockAdapterAdminLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: LockModelAdmin) {
            binding.txtLockName.text = model.lock.internal_id
            binding.txtModelName.text = "Lock Id: ${model.lock.unique_key}"
            binding.txtDate.text =
                CommonValues.dateZformat(model.lock.createdAt, contxt)

            if (model.lock.alloted) {
                binding.deleteAccess.isVisible = false
                binding.txtStatus.text = "Status: Assigned"
            } else {
                binding.deleteAccess.isVisible = false
                when (model.lock.inventory_status) {
                    1 -> {
                        binding.txtStatus.text = "Status: Incomplete info"
                    }
                    2 -> {
                        binding.txtStatus.text = "Status: Ready for Sale"
                    }
                    3 -> {
                        binding.txtStatus.text = "Status: Blocked for Order"
                    }
                    4 -> {
                        binding.txtStatus.text = "Status: Ready for Resale"
                    }
                    5 -> {
                        binding.txtStatus.text = "Status: Not for sale"
                    }
                    6 -> {
                        binding.txtStatus.text = "Status: Blocked"
                    }
                }
            }

            binding.mainLay.setOnClickListener {
                listener.clickForCard(model)

//            if (model.lock.alloted) {
//                defaultDialog(contxt , "This lock is already assigned, you cannot delete this lock!", object : OnActionOK {
//                    override fun onClickData() {
//                    }
//                })
//            } else {
//                listener.clickDelete(model.lock._id,model.lock.unique_key,model.lock.access_key)
//            }
            }
        }
    }

    override fun getItemCount() = listFiltered.size

    @SuppressLint("SetTextI18n")
    @RequiresApi(Build.VERSION_CODES.O)
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val model = listFiltered[position]
        holder.bind(model)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateList(locks: ArrayList<LockModelAdmin>) {
        adminLockData = locks
        listFiltered = adminLockData
        notifyDataSetChanged()
    }

    interface ClickToConnect {
        fun clickForCard(model: LockModelAdmin)
    }

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                val charString = constraint?.toString() ?: ""
                listFiltered = if (charString.isEmpty()) {
                    adminLockData
                } else {
                    val mFilteredList = ArrayList<LockModelAdmin>()
                    adminLockData.filter {
                        (
                            it.lock.internal_id.contains(
                                constraint!!,
                                true
                            ) || it.lock.unique_key.contains(constraint!!, true)
                            ) or
                            (
                                it.lock.internal_id.startsWith(
                                    constraint,
                                    true
                                ) || it.lock.unique_key.startsWith(constraint, true)
                                )
                    }
                        .forEach { mFilteredList.add(it) }
                    mFilteredList
                }
                return FilterResults().apply { values = listFiltered }
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                listFiltered = if (results?.values == null) {
                    ArrayList()
                } else {
                    results.values as java.util.ArrayList<LockModelAdmin>
                }
                notifyDataSetChanged()
            }
        }
    }
}