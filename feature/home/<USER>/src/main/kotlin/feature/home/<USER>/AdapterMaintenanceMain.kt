package feature.home.installer

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import data.network.android.models.ModelData
import keyless.feature.home.installer.databinding.LayoutMaintenanceBinding

class AdapterMaintenanceMain(context: InstallerListFragment) :
    RecyclerView.Adapter<AdapterMaintenanceMain.ViewHolder>() {

    var listOptions: ArrayList<ModelData> = ArrayList()
    var listener = context as ClickToMaintain
    lateinit var contextMain: Context

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        contextMain = viewGroup.context
        val binding = LayoutMaintenanceBinding.inflate(
            LayoutInflater.from(contextMain),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(listOptions[position], position)
    }

    override fun getItemCount() = listOptions.size

    fun updateOptions(listMain: ArrayList<ModelData>) {
        listOptions = listMain
        notifyDataSetChanged()
    }

    inner class ViewHolder(val binding: LayoutMaintenanceBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: ModelData, position: Int) {
            binding.txtLockName.text = model.lock_info[0].internal_id
            binding.txtCompanyName.text = contextMain.getString(keyless.data.utils.android.R.string.company_name_api) +
                " " + model.company[0].company_name
            binding.txtAddress.text = contextMain.getString(keyless.data.utils.android.R.string.address_api) +
                " " + model.maplockproperty.appartment_number +
                ", " + model.maplockproperty.floor_number + " Floor " + model.property_details.building_name
            binding.mainLay.setOnClickListener {
                listener.clickToUpdateStatus(model, position)
            }
        }
    }

    interface ClickToMaintain {
        fun clickToUpdateStatus(model: ModelData, position: Int)
    }
}