plugins {
    id("keyless.android.feature")
    id("org.jetbrains.kotlin.kapt")
}

android {
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

dependencies {
    implementation(project(":data-common"))
    implementation(project(":data-network-android"))
    implementation(project(":data-utils-android"))
    implementation(project(":feature-common"))
    implementation(project(":feature-dashboard"))

    implementation(keyless.androidx.lifecycle.livedata)
    implementation(keyless.retrofit)

    implementation("com.google.android.gms:play-services-maps:18.1.0")
    implementation("com.google.android.gms:play-services-location:21.0.1")

    implementation(platform("com.google.firebase:firebase-bom:28.4.2"))
    implementation ("com.google.firebase:firebase-crashlytics-ktx")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")

    implementation(keyless.glide)
    kapt(keyless.glide.compiler)
}