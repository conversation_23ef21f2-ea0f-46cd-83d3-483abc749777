package feature.injection.domain

import domain.settings.checkin.ViewModel
import domain.settings.checkin.injection.CheckInSettingsDomainScope
import org.koin.dsl.module

internal val checkInDomainModule = module {
    scope<CheckInSettingsDomainScope> {
        scoped {
            ViewModel(
                repository = get(),
                logger = get(),
                status = get()
            )
        }
    }
}