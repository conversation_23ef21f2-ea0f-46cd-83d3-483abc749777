package feature.injection.domain

import domain.settings.company.staff.add.injection.AddStaffDomainScope
import domain.settings.company.staff.manage.ViewModel
import domain.settings.company.staff.manage.injection.ManageStaffDomainScope
import org.koin.dsl.module

val manageStaffDomainInjectionModule = module {
    scope<ManageStaffDomainScope> {
        scoped {
            ViewModel(
                companyStaff = get(),
                logger = get(),
                status = get()
            )
        }
    }
}

val addStaffDomainInjectionModule = module {
    scope<AddStaffDomainScope> {
        scoped {
            domain.settings.company.staff.add.ViewModel(
                staff = get(),
                logger = get(),
                status = get()
            )
        }
    }
}