package feature.injection.domain

import domain.regula.ViewModel
import domain.regula.injection.RegulaDomainScope
import org.koin.dsl.module

internal val regulaDomainModule = module {
    scope<RegulaDomainScope> {


        scoped {
            ViewModel(
                documents = get(),
                status = get(),
                logger = get(),
                face = get(),
                user = get()
            )
        }
    }
}