plugins {
    id("common.library")
    id("android.target.library")
    id("jvm.target.library")
//    id("ios.target.library")
}

android {
    androidResources {
        noCompress += "Regula/faceSdkResource.dat"
    }

    aaptOptions {
        noCompress += "Regula/faceSdkResource.dat"
    }
}

commonDependencies {
    implementation(project(":core-common"))
    implementation(project(":core-monitoring-common"))
    implementation(project(":core-regula-common"))
}

androidDependencies {
    api ("com.regula.face:api:6.2.3222") { isTransitive = true }
    api ("com.regula.face.core:basic:6.2.547")

//    api ("com.regula.face:core:5.2.232@aar")
//    api ("com.regula.face:api:5.2.2825@aar") { isTransitive = true }
}