plugins {
    id("common.library")
    id("android.target.library")
    id("jvm.target.library")
//    id("ios.target.library")
}

commonDependencies {
    implementation(project(":core-common"))
    implementation(project(":core-monitoring-common"))
    implementation(project(":core-regula-common"))
}

androidDependencies {
    api ("com.regula.documentreader.core:full:7.2.+@aar")
    api ("com.regula.documentreader:api:7.2.+@aar") { isTransitive = true }
 
//    api ("com.regula.documentreader.core:full:6.9.+@aar")
//    api ("com.regula.documentreader:api:6.9.+@aar") { isTransitive = true }
}