plugins {
    id("common.library")
    id("android.target.library")
    id("jvm.target.library")
    // id("ios.target.library")
}

commonDependencies {
    implementation(project(":core-common"))
    implementation(project(":core-monitoring-common"))
    api(project(":core-http-download-common"))
    api(project(":core-lock-common"))
}

androidDependencies {
    api(project(":core-lock-airbnk-sdk"))
}

android {
    packagingOptions {

        excludes += "resources.arsc"
        excludes += "AndroidManifest.xml"
    }
}