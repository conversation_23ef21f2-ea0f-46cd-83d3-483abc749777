plugins {
    id("common.library")
    id("android.target.library")
    id("jvm.target.library")
    // id("ios.target.library")
}

commonDependencies {
    implementation(project(":core-common"))
    api(project(":core-lock-iseo-sdk"))
    implementation(project(":core-monitoring-common"))
    api(project(":core-http-download-common"))
    implementation(project(":core-lock-common"))
}

androidDependencies {
    implementation("org.bouncycastle:bcprov-jdk15on:1.59")
    implementation("org.msgpack:msgpack-core:0.8.16")
    implementation("commons-codec:commons-codec:1.10")
    implementation(project(mapOf("path" to ":core-caching-key-value")))
}