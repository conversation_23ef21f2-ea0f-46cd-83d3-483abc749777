package core.lock.iseo

import core.common.platform.Platform
import core.lock.common.errors.LockException
import core.monitoring.common.repository.Logger

internal inline fun <T> handler(logger: Logger, job: () -> T): T {
    return try {
        job()
    } catch (ex: Exception) {
        val error = LockException(cause = ex, location = Platform.executeLocation(), details = mapOf())
        logger.error(error)
        throw error
    }
}