package com.messerschmitt.mstblelib;

import android.util.Base64;
import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import static java.lang.System.*;

/**
 * Class that holds information of the given mstsmartkey provided by push/sms/mms/email/passbook etc
 * which holds the following information for easy access:
 * system code: project/hotel specific indentifier
 * -> for Ble Communication purposes only
 * pRoomlist: Array of physical room numbers of guestrooms, which could be opened
 * -> for Ble Communication purposes only
 * lRoomlist: Array of physical room numbers of guestrooms, which could be opened
 * -> could be used to display in activities to show it to the db_user
 * specials: Array of unlocked special areas, which can be accessed with the keydata
 * -> for Ble Communication purposes only
 * keydata: the data, which is sent to the Messerschmitt Reader Unit to open the door
 * -> for Ble Communication purposes only
 * enddate: end date of validity of keydata / MSTMobile Key
 * -> could be used to display in activities to show it to the db_user
 * optional: startdate: start date of validity of keydata / MSTMobile Key(e.g. used for PreCheckin purposes)
 * -> could be used to display in activities to show it to the db_user
 * -> has to be checked for availability
 * optional: Guest ID: unique id of the guest/app db_user(e.g. Membership ID)
 * -> for checking, if the db_user is permitted to use the Mobile Key(checking should be done by sdk users)
 *
 * <AUTHOR> Dremel@Messerschmitt Systems GmbH
 * @version 2020.1001
 */
public class MSTsmartkey {
    // JSON Node Names
    private static final String TAG = "SMARTKEY";

    public static final byte[][] dhStaticDecodeMandant = new byte[][] {{0x1C,0x25,0x32,0x6F},{0x7D,0x68,0x2A,0x50}};

    private final ArrayList<Integer> pRoomlist;
    private final ArrayList<String> lRoomlist;
    private final byte[] keydata;  // contains key bytes + token bytes
    private byte[] specials;
    private int systemcode;
    private Date enddate;
    private Date startdate;
    private String guestID;


    /**
     * creates an instance of mstsmartkey by deserilization of the information provided/received by push/sms/passbook etc,
     *
     * @param rSmartkey Json string provided by push/sms/passbook etc.
     * @param device_id Device specific id, which is used for authentification on the door unit
     *
     */
    public MSTsmartkey(String rSmartkey, byte[] device_id) {
        keydata = new byte[56];
        byte[] data;
        pRoomlist = new ArrayList<>();
        lRoomlist = new ArrayList<>();

        if ((rSmartkey.length() > 32) && (rSmartkey.charAt(0) != '{') && (rSmartkey.charAt(rSmartkey.length() - 1) != '}')) {
            try {
                rSmartkey = rSmartkey.replace("\r", "");
                rSmartkey = rSmartkey.replace("\n", "");
                rSmartkey = rSmartkey.replace("\t", "");
                data = Base64.decode(rSmartkey, Base64.DEFAULT);

                if ((data.length > 32) && (data[0] != '{') && (data[data.length-1] != '}')) {
                    try {
                        byte[] dhExtern = new byte[]{data[0], data[1], data[2], data[3]};
                        byte[] dhSecret = MSTUtils.DH32powmod(dhExtern, dhStaticDecodeMandant[0]);
                        byte[] dhValues = new byte[data.length-6]; arraycopy(data, 6, dhValues, 0, dhValues.length);
                        byte[] CFB = new byte[4];
                        for (int i = 0; i < 4; i++) {
                            CFB[i] = MSTUtils.IntAsByte(MSTUtils.ByteAsInt(dhStaticDecodeMandant[1][i]) ^ MSTUtils.ByteAsInt(dhExtern[i]));
                        }

                        for (int i = 0; i < dhValues.length; i++) {
                            dhValues[i] = MSTUtils.IntAsByte(MSTUtils.ByteAsInt(dhValues[i]) ^ MSTUtils.ByteAsInt(dhSecret[i & 0x03]));
                            dhValues[i] = MSTUtils.IntAsByte(MSTUtils.ByteAsInt(dhValues[i]) ^ MSTUtils.ByteAsInt(CFB[i & 0x03]));
                            CFB[i & 0x03] = MSTUtils.IntAsByte(MSTUtils.ByteAsInt(dhValues[i]) ^ MSTUtils.ByteAsInt(CFB[i & 0x03]));
                        }

                        int crc16 = MSTUtils.MSTbleCalcCRC16(dhValues.length, dhValues, dhValues.length, 0);
                        if (crc16 != MSTUtils.BytesToWordLH(data, 4)) {
                            for (int i = 0; i < dhValues.length; i++) {
                                dhValues[i] = 0;
                            }
                        }

                        rSmartkey = new String(dhValues, StandardCharsets.UTF_8);
                    } catch (Exception ex) {
                        Log.v(TAG, "DH32 Exception", ex);
                    }
                } else {
                    rSmartkey = new String(data, StandardCharsets.UTF_8);
                }
            } catch (Exception ex) {
                Log.v(TAG, "Base64 Exception", ex);
            }
        }

        try{
            String text = new String(device_id, StandardCharsets.UTF_8);
            byte[] token = MSTUtils.hexStringToByteArray(MSTUtils.md5(text));
            JSONObject json = new JSONObject(rSmartkey);
            if (json.has("sf")) {
                JSONObject c = json.getJSONObject("sf");
                lRoomlist.add(c.getString("l"));
                pRoomlist.clear();
            } else {
                if (json.has("rm")) {
                    JSONArray rmArray = json.getJSONArray("rm");

                    for (int i = 0; i < rmArray.length(); i++) {
                        JSONObject c = rmArray.getJSONObject(i);
                        pRoomlist.add(c.getInt("p"));
                        lRoomlist.add(c.getString("l"));
                    }
                } else {
                    pRoomlist.clear();
                    lRoomlist.clear();
                }
            }
            if (json.has("g#")) guestID = json.getString("g#");
            else guestID = "";

            if (json.has("sc")) systemcode = json.getInt("sc");
            else systemcode = 77;

            if (json.has("to"))
                enddate = MSTUtils.parseDateTime(json.getString("to"));
            else if (json.has("da"))
                enddate = MSTUtils.parseDateTime(json.getString("da"));
            else enddate = null;

            if (json.has("at"))
                startdate = MSTUtils.parseDateTime(json.getString("at"));
            else if (json.has("das"))
                startdate = MSTUtils.parseDateTime(json.getString("das"));
            else startdate = null;

            if (json.has("s3"))
                specials = android.util.Base64.decode(json.getString("s3"), android.util.Base64.DEFAULT);
            else if (json.has("sa"))
                specials = MSTUtils.getByteArrayFromJsonArray(json.getJSONArray("sa"));
            else specials = null;

            if (json.has("d3"))
                data = android.util.Base64.decode(json.getString("d3"), android.util.Base64.DEFAULT);
            else if (json.has("dt"))
                data = MSTUtils.getByteArrayFromJsonArray(json.getJSONArray("dt"));
            else data = null;

            assert data != null;
            arraycopy(data, 0, keydata, 0, 40);
            arraycopy(token, 0, keydata, 40, 16);

            if (json.has("a3"))
                data = android.util.Base64.decode(json.getString("a3"), android.util.Base64.DEFAULT);
            else data = null;
            if (data != null) {
                arraycopy(data, 0, MSTUtils.dhAuthStaticValues[0], 0, 4);
                arraycopy(data, 4, MSTUtils.dhAuthStaticValues[1], 0, 4);
                arraycopy(data, 8, MSTUtils.dhAuthStaticValues[2], 0, 4);
                arraycopy(data, 12, MSTUtils.dhAuthStaticValues[3], 0, 4);
            }

        }catch (JSONException e){
            Log.v(TAG, "JSON Exception", e);
        }
    }
    /**
     * returns the system code of the mobile key
     * for ble scan purposes
     * not necessary to display in gui
     * @return int system code
     */
    public int getSystemcode(){
        return systemcode;
    }

    /**
     * returns the list of valid rooms to access;
     * this is the list of logical room numbers!!
     * Note: they can differ from the physical room numbers(e.g A2345 -> logical room number; 234 -> physical room number)
     * it is suggested to use logical room numbers for displaying the numbers in the gui
     * if array count < 1, the mobile key is invalid
     * @return ArrayList<Integer> Rooms
     */
    public ArrayList<Integer> Rooms(){
        return pRoomlist;
    }

    /**
     * returns the list of valid rooms to access;
     * this is the list of logical room numbers!!
     * Note: they can differ from the physical room numbers(e.g A2345 -> logical room number; 234 -> physical room number)
     * it is suggested to use logical room numbers for displaying the numbers in the gui
     * if array count < 1, the mobile key is invalid
     * @return ArrayList<Integer> enabledRooms
     */
    public ArrayList<String> enabledRooms(){
        return lRoomlist;
    }
    /**
     * returns the expiry date of the mobile key
     * if null, the mobile key is invalid
     * @return Date enddate
     */
    public Date validTo(){
        return enddate;
    }

    /**
     * returns the start date of the mobile key
     * if null, no startdate given, the mobile key is valid immediately
     * @return Date startdate
     */
    public Date validFrom(){
        return startdate;
    }

    /**
     * returns the list of activated special areas in the mobile key
     * for ble scan purposes
     * not necessary to display in gui
     * @return byte[] specials
     */
    public byte[] getSpecials(){
        return specials;
    }

    /**
     * returns the the Guest ID from the mobile key
     * can be used by sdk db_user to check for correct db_user
     * @return Guest ID as String
     */
    public String getGuestID(){
        return guestID;
    }

    /**
     * returns the the keydata to open the door
     * for ble scan purposes
     * not necessary to display in gui
     * @return byte[] keydata
     */
    public byte[] getKeydata(){
        return keydata;
    }


    /**
     * returns the validity of the data, which was provided by instancing the mstsmartkey object
     * @return boolean valid
     */
    public boolean valid(){
        return (systemcode > 0) && (specials.length > 0) && (keydata.length > 0);
    }

}
