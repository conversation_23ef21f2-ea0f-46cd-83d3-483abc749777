package com.messerschmitt.mstblelib;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothProfile;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanCallback;
import android.bluetooth.le.ScanResult;
import android.content.Context;
import android.content.Intent;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.Objects;


/**
 * Class that do complete ble communication process to open a Messerschmitt Door Unit(MST reader device)
 *
 * <AUTHOR> Systems AG
 * @version 2020.1001
 */
public class MSTBleInterface {
    //private static final String TAG = "SMARTKEY";

    private boolean mScanning;
    private boolean mNotiEnabled;
    private boolean mNoti;
    private boolean mWaitResp;
    private int lvl;
    private final MSTBleUtils mBluetoothUtils;
    private final MSTsmartkey mSmartkey;
    private final Context mContext;
    private MSTBleDevice mstBleDevice;
    private BluetoothLeScanner mBleScanner;
    private BluetoothGatt mGatt;
    private BluetoothGattCharacteristic mstReadCharacteristic;
    private BluetoothGattCharacteristic mstWriteCharacteristic;
    private byte[] rxBuf;
    private ByteBuffer rx;
    private int rxMode;

    private static final int CALLBACK_DISCOVER_DEVICES = 0;                         //~ 3000ms
    private static final int CALLBACK_CONNECT_DISCOVER_SERVICES = 1;                //~ 300ms
    private static final int CALLBACK_WAIT_DEVICE_CHALLENGE = 2;                    //~ max 400ms
    private static final int CALLBACK_APP_RESPONSE = 3;                             //~ 20ms
    private static final int CALLBACK_WAIT_DEVICE_RESPONSE = 4;                     //~ max 400ms
    private static final int CALLBACK_SEND_KEYDATA = 5;                             //~ 20ms
    private static final int CALLBACK_SEND_KEYDATA2 = 6;                             //~ 20ms
    private static final int CALLBACK_SEND_KEYDATA3 = 7;                             //~ 20ms
    private static final int CALLBACK_SEND_KEYDATA4 = 8;                             //~ 20ms
    private static final int CALLBACK_WAIT_KEY_MESSAGE_DATA = 9;                    //~ max 400ms
    private static final int CALLBACK_WAIT_DOOR_STATE = 10;                          //~ max 5000ms
    private static final int CALLBACK_WAIT_DISCONNECT = 11;                          //~ 200ms
    private static final int CALLBACK_TIMEOUT =         12;                          //~ 10000ms
    private static final int CALLBACK_TIMEOUT_DISCOVER_SERVICES =         13;       //~ 3000ms
    private static final int CALLBACK_DELAY_DEVICENAME =         14;       //~ 3000ms
    private static final int CALLBACK_TIMEOUT_DEVICENAME =         15;       //~ 3000ms
    /*<NEW>*/
    private static final int CALLBACK_OVERALL_TIMEOUT = 16;
    private static final int CALLBACK_WAIT_DOOR_STATE_EX = 17;                          //~ max 5000ms
    private byte[] mResponseData = new byte[17];

    private static final int RX_MODE_AUTH =          0;
    private static final int RX_MODE_NAME =          1;
    private static final int RX_MODE_KEY  =          2;
    private static final int RX_MODE_SCAN =          4;
    private static final int RX_MODE_KEY_EX =        5;

    private final Handler mHandler;
    private final Runnable rDiscover_Devices;
    private final Runnable rConnect_Discover_Services;
    private final Runnable rWait_Device_Challenge;
    private final Runnable rApp_Response;
    private final Runnable rWait_Device_Response;
    private final Runnable rSend_Keydata;
    private final Runnable rSend_Keydata2;
    private final Runnable rSend_Keydata3;
    private final Runnable rSend_Keydata4;
    private final Runnable rWait_Key_Message_Data;
    private final Runnable rWait_Door_State;
    private final Runnable rWait_Door_StateEx;
    private final Runnable rWait_Disconnect;
    private final Runnable rTimeout;
    private final Runnable rTimeout_disc_service;
    private final Runnable rDelay_DeviceName;
    private final Runnable rTimeout_DeviceName;
    private final Runnable rTimeout_OverallTimeout;

    public final static String ACTION_START_OPEN =
            "com.messerschmitt.mstblelib.ACTION_START_OPEN";
    public final static String ACTION_SCAN_TIMEOUT =
            "com.messerschmitt.mstblelib.ACTION_SCAN_TIMEOUT";
    public final static String ACTION_CONNECT_TO =
            "com.messerschmitt.mstblelib.ACTION_CONNECT_TO";
    public final static String ACTION_SPECIAL_NAME =
            "com.messerschmitt.mstblelib.ACTION_SPECIAL_NAME";
    public final static String ACTION_OPEN_THE_DOOR =
            "com.messerschmitt.mstblelib.ACTION_OPEN_THE_DOOR";
    public final static String ACTION_OPEN_THE_DOOR_FAILED =
            "com.messerschmitt.mstblelib.ACTION_OPEN_THE_DOOR_FAILED";
    public final static String ACTION_FINISH_OPEN =
            "com.messerschmitt.mstblelib.ACTION_FINISH_OPEN";
    public final static String ACTION_DATE_TIME =
            "com.messerschmitt.mstblelib.ACTION_DATE_TIME";
            public final static String ACTION_BATT_STATUS =
            "com.messerschmitt.mstblelib.ACTION_BATT_STATUS";
    public final static String ERROR_ENABLE_NOTIFY =
            "com.messerschmitt.mstblelib.ERROR_ENABLE_NOTIFY";
    public final static String ERROR_DEVICE_RESPONSE =
            "com.messerschmitt.mstblelib.ERROR_DEVICE_RESPONSE";
    public final static String ERROR_KEY_RESULT =
            "com.messerschmitt.mstblelib.ERROR_KEY_RESULT";
    public final static String ERROR_DOOR_STATE =
            "com.messerschmitt.mstblelib.ERROR_DOOR_STATE";
    public final static String ERROR_CONNECT =
            "com.messerschmitt.mstblelib.ERROR_CONNECT";
    public final static String ERROR_DISCOVER_SERVICES =
            "com.messerschmitt.mstblelib.ERROR_DISCOVER_SERVICES";
    public final static String ERROR_AUTH_FAILED =
            "com.messerschmitt.mstblelib.ERROR_AUTH_FAILED";
    public final static String EXTRA_DATA =
            "com.messerschmitt.mstblelib.EXTRA_DATA";
    public static String uniqueKeyMain =
            "";

    /**
     * creates an instance of mstsmartkeyInterface. It provides the method "mstOpenDoor", that do the complete
     * ble communication process to open a Messerschmitt Door Unit
     * @param bluetoothUtils Wrapper class, which must be instaciated by the sdk-db_user, which provides access to system ble functions
     * @param context the context of the calling activity,service etc...
     * @param smartkey the mstsmartkey, which must be instanciated by the sdk-db_user, which provides necessary keydata to communicate with Messerschmitt Door Unit
     */
    //@TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public MSTBleInterface(final MSTBleUtils bluetoothUtils, Context context, final MSTsmartkey smartkey) {

        mBluetoothUtils = bluetoothUtils;
        mSmartkey = smartkey;
        mContext = context;

        mBleScanner = bluetoothUtils.getBluetoothAdapter().getBluetoothLeScanner();

        mHandler = new Handler();
        rDiscover_Devices           = runnables(CALLBACK_DISCOVER_DEVICES);
        rConnect_Discover_Services  = runnables(CALLBACK_CONNECT_DISCOVER_SERVICES);
        rWait_Device_Challenge  = runnables(CALLBACK_WAIT_DEVICE_CHALLENGE);
        rApp_Response           = runnables(CALLBACK_APP_RESPONSE);
        rWait_Device_Response   = runnables(CALLBACK_WAIT_DEVICE_RESPONSE);
        rSend_Keydata           = runnables(CALLBACK_SEND_KEYDATA);
        rSend_Keydata2          = runnables(CALLBACK_SEND_KEYDATA2);
        rSend_Keydata3          = runnables(CALLBACK_SEND_KEYDATA3);
        rSend_Keydata4          = runnables(CALLBACK_SEND_KEYDATA4);
        rWait_Key_Message_Data  = runnables(CALLBACK_WAIT_KEY_MESSAGE_DATA);
        rWait_Door_State        = runnables(CALLBACK_WAIT_DOOR_STATE);
        rWait_Disconnect        = runnables(CALLBACK_WAIT_DISCONNECT);
        rTimeout                = runnables(CALLBACK_TIMEOUT);
        rTimeout_disc_service   = runnables(CALLBACK_TIMEOUT_DISCOVER_SERVICES);
        rDelay_DeviceName       = runnables(CALLBACK_DELAY_DEVICENAME);
        rTimeout_DeviceName     = runnables(CALLBACK_TIMEOUT_DEVICENAME);
        /*<NEW>*/
        rTimeout_OverallTimeout = runnables(CALLBACK_OVERALL_TIMEOUT);
        rWait_Door_StateEx        = runnables(CALLBACK_WAIT_DOOR_STATE_EX);

        mScanning = false;
        mNotiEnabled = false;
        mNoti = false;
     /*   mRXChallenge = false;
        mKeyData = false;*/
        mWaitResp = false;

        mResponseData =null;
        rxMode =-1;

    }

//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
//device Communication
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    /**
     * creates and holds all timers in this function
     *
     *  CALLBACK_DISCOVER_DEVICES ~ 3000ms -> scan period for MST reader devices,/n
     *      stops complete door opening process, will be stopped, if a correct reader device found/n
     *  CALLBACK_CONNECT_DISCOVER_SERVICES ~ 300ms -> waits after device is connected and services are discovered,/n
     *      before notification of connected device will be enabled/n
     *  CALLBACK_WAIT_DEVICE_CHALLENGE ~ max 400ms -> waits max. 400ms for response of reader device ,after enabling notification;/n
     *      stops communication and disconnect from reader device,if no response in the given time period;/n
     *      stops, if a response from reader device was received/n
     *  CALLBACK_APP_RESPONSE ~ 20ms -> waits before response from app will be sent, after getting device challenge request/n
     *      (see CALLBACK_WAIT_DEVICE_CHALLENGE)/n
     *  CALLBACK_WAIT_DEVICE_RESPONSE ~ max 400ms -> waits max. 400ms for response of reader device ,after sending App Response(see CALLBACK_APP_RESPONSE);/n
     *      stops communication and disconnect from reader device,if no response in the given time period;/n
     *      stops, if a response from reader device was received/n
     *  CALLBACK_SEND_KEYDATA ~ 20ms -> wait before sending block 1 of keydata/n
     *  CALLBACK_SEND_KEYDATA2 ~ 20ms -> wait before sending block 2 of keydata/n
     *  CALLBACK_SEND_KEYDATA3 ~ 20ms -> wait before sending block 3 of keydata/n
     *  CALLBACK_WAIT_KEY_MESSAGE_DATA ~ max 400ms waits max. 400 ms for response of reader device after sending key data/n
     *      stops communication and disconnect from reader device,if no response in the given time period;/n
     *      stops, if a response from reader device was received/n
     *  CALLBACK_WAIT_DOOR_STATE ~ max 5000ms -> waits max 5s for response of reader device for door state door open/close/n
     *      stops communication and disconnect from reader device,if no response in the given time period;/n
     *      stops, if a response from reader device was received/n
     *  CALLBACK_WAIT_DISCONNECT ~ 200ms -> waits before app disconnects from reader device/n
     *  CALLBACK_TIMEOUT ~ 10000ms -> if the complete process takes longer than 10s disconnect from reader device/n
     *      stops, if the app is connected to the reader device/n
     * @param param type of timer, which should be started/n
     */
    private Runnable runnables(final int param){

        return new Runnable() {
            @Override
            public void run() {
                switch (param){
                    case CALLBACK_DISCOVER_DEVICES:
                        broadcastUpdate(ACTION_SCAN_TIMEOUT);
                        mHandler.removeCallbacks(rTimeout);
                        stopLeScan();
                        break;
                    case CALLBACK_CONNECT_DISCOVER_SERVICES:
                        rxMode = RX_MODE_AUTH;
                        enableNotification();
                        break;
                    case CALLBACK_WAIT_DEVICE_CHALLENGE:
                        if(lvl < 5) {
                            if (!mNotiEnabled) {
                                if(!mNoti) {
                                    mNoti=true;
                                    enableNotification();
                                }else{
                                    broadcastUpdate(ERROR_ENABLE_NOTIFY);
//                                    closeGatt();
                                }
                            }
                        }
                        break;
                    case CALLBACK_APP_RESPONSE:
                        sendAppResponse(mstBleDevice.getAppResponseChallenge());
                        break;
                    case CALLBACK_WAIT_DEVICE_RESPONSE:
                        if(lvl < 7) {
                            if(!mWaitResp){
                                mWaitResp=true;
                                mHandler.removeCallbacks(rWait_Device_Response);
                                sendAppResponse(mstBleDevice.getAppResponseChallenge());
                            }else {
                                disconnectFromMstDevice();
                                broadcastUpdate(ERROR_DEVICE_RESPONSE);
                            }
                        }
                        break;
                    case CALLBACK_SEND_KEYDATA:
                        rxMode = RX_MODE_KEY;
                        sendBlock(0);
                        break;
                    case CALLBACK_SEND_KEYDATA2:
                        sendBlock(1);
                        break;
                    case CALLBACK_SEND_KEYDATA3:
                        sendBlock(2);
                        break;
                    case CALLBACK_SEND_KEYDATA4:
                        sendBlock(3);
                        break;
                    case CALLBACK_WAIT_KEY_MESSAGE_DATA:
                        if(lvl < 9) {
                            broadcastUpdate(ERROR_KEY_RESULT);
                            disconnectFromMstDevice();
                        }
                        break;
                    case CALLBACK_WAIT_DOOR_STATE:
                        if(lvl < 10) {
                            disconnectFromMstDevice();
                            broadcastUpdate(ERROR_DOOR_STATE);
                        }
                        break;
                    case CALLBACK_WAIT_DISCONNECT:
                        closeGatt();
                        break;
                    case CALLBACK_TIMEOUT:
                        //connect to MST device takes longer than 10seconds
                        broadcastUpdate(ERROR_CONNECT);
//                        closeGatt();
                        break;
                    case CALLBACK_TIMEOUT_DISCOVER_SERVICES:
                        broadcastUpdate(ERROR_DISCOVER_SERVICES);
//                        closeGattErr();
                        break;
                    case CALLBACK_DELAY_DEVICENAME:
                        rxMode = RX_MODE_NAME;
                        getReaderDescription();
                        break;
                    case CALLBACK_TIMEOUT_DEVICENAME:
                        getRoomNameFailed();
                        break;
                    case CALLBACK_OVERALL_TIMEOUT:
//                        closeGattErr();
                        break;
                    case CALLBACK_WAIT_DOOR_STATE_EX:
                        if(lvl < 10) {
                            broadcastUpdate(ERROR_DOOR_STATE);
                        }
                        break;
                }
            }
        };
    }

    private void checkCharacteristicChange(byte[] changeData){
        int i;

        if((changeData ==null) || (changeData.length==0)) return;

        if(MSTUtils.ByteAsInt(changeData[0])==0xff) {
            deviceTimeout();
            return;
        }

        if(changeData.length>1) {
            if ((MSTUtils.ByteAsInt(changeData[0]) & 0xF0) > 0x00) {

                if ((MSTUtils.ByteAsInt(changeData[0]) & 0x0F) == 0x00) {
                    rxBuf = new byte[((MSTUtils.ByteAsInt(changeData[0])>>4)+1)*changeData.length-1];
                    for (i = 0; i < rxBuf.length; i++)
                        rxBuf[i] = (byte) 0xFF;
                    rx = ByteBuffer.wrap(rxBuf);
                }
                rx.put(Arrays.copyOfRange(changeData, 1, changeData.length));
            }else{
                rxBuf = Arrays.copyOfRange(changeData,1,changeData.length);
            }

            if ((MSTUtils.ByteAsInt(changeData[0]) >> 4) != (MSTUtils.ByteAsInt(changeData[0]) & 0x0F))
                return;


        } else {
                rxBuf = Arrays.copyOf(changeData,changeData.length);
        }


        if(rxBuf.length > 0){
            switch (rxBuf[0]){
                case 0x01:
                    if(rxMode == RX_MODE_AUTH) {
                        switch (rxBuf[1] & 0x6E) {
                            case 0x03:
                            case 0x02:
                            case 0x13:
                            case 0x12:
                            case 0x11:
                            case 0x10:
                                if(rxBuf.length > 15) {
                                    mHandler.removeCallbacks(rWait_Device_Challenge);
                                    mNotiEnabled = true;
                                    deviceChallenge(Arrays.copyOf(rxBuf, 16));
                                }
                                break;
                            case 4:
                                if(rxBuf.length > 10) {
                                    mHandler.removeCallbacks(rWait_Device_Response);
                                    deviceResponse(Arrays.copyOf(rxBuf, 11));
                                }
                                break;
                            case 6:
                                if(rxBuf.length > 15) {
                                    mHandler.removeCallbacks(rWait_Device_Response);
                                    deviceResponse(Arrays.copyOf(rxBuf, 16));
                                }
                                break;
                            default:
                                notSupportedAuthMode();
                                break;
                        }
                    }
                    break;
                case 0x02:
                    if(rxMode == RX_MODE_KEY) {
                        if (rxBuf.length == 16) {
                            mHandler.removeCallbacks(rWait_Key_Message_Data);
                            decodeKeyResult(Arrays.copyOf(rxBuf, 16));
                            break;
                        }
                        if (rxBuf.length == 1) {
                            mHandler.removeCallbacks(rWait_Door_State);
                            doorStateReceived();
                            break;
                        }
                        if (rxBuf.length == 2) {
                            rxMode = RX_MODE_KEY_EX;
                            mHandler.removeCallbacks(rWait_Door_State);

                            mHandler.postDelayed(rWait_Door_StateEx, 4000);

                            break;
                        }

                    }
               //     }
                    break;
                case 0x03:
                    if(rxBuf.length == 1){
                        //ignore this error
                    }

                    if(rxBuf.length > 1){
                        switch (rxBuf[1]){
                            //case 0: authFailed();
                            case 1: authFailed();
                            case 2: authFailed();
                            case 3: authFailed();
                            case 4: authFailed();
                            case 5: authFailed();
                            case 6: authFailed();
                            case 7: authFailed();
                            case 8: authFailed();
                            //default: authFailed();
                        }
                    }
                    break;
                case 0x00:
                case (byte)0xff:
                    break;
                default:
                    if(rxMode == RX_MODE_NAME) {
                        publishRoomName(rxBuf);
                    }
                    break;
            }
        }
    }

    /**
     * enables the Notification of MSTReadCharacteristic
     * and starts the CALLBACK_WAIT_DEVICE_CHALLENGE timer
     */
    // TODO Remove Suppress
    @SuppressLint("MissingPermission")
    private void enableNotification(){
        try {
            mNotiEnabled = false;
            mHandler.postDelayed(rWait_Device_Challenge, 400);
            mGatt.setCharacteristicNotification(mstReadCharacteristic, true);
            BluetoothGattDescriptor descriptor = mstReadCharacteristic.getDescriptor(MSTUtils.MSTNotifyDescriptorUUID);
            descriptor.setValue(BluetoothGattDescriptor.ENABLE_INDICATION_VALUE);
            mGatt.writeDescriptor(descriptor);
            lvl = 4;
        }catch(UnknownError error){
            //error.printStackTrace();
        }
    }

    /**
     * checks the challenge data after notification was enabled successfully
     * and decodes the appResponse Data for reply;
     * starts the CALLBACK_APP_RESPONSE timer
     * @param challengeData data, for begin the authentification
     */
    private void deviceChallenge(byte[] challengeData){
        mHandler.removeCallbacks(rWait_Device_Challenge);
        lvl = 5;
        mNotiEnabled = true;
        mResponseData = null;
        if(mstBleDevice.checkDeviceChallenge(challengeData)){
            mHandler.postDelayed(rApp_Response, 100);
            broadcastUpdate(ACTION_DATE_TIME,mstBleDevice);
        }else{
            broadcastUpdate(ERROR_DEVICE_RESPONSE);
            disconnectFromMstDevice();
        }

    }
    /**
     * sends the APP response Data which was generated in device challenge,
     * after CALLBACK_APP_RESPONSE timer was finished
     * @param responseData data, which used for app response
     */
    // TODO Remove Suppress
    @SuppressLint("MissingPermission")
    private void sendAppResponse(byte[] responseData){
        lvl = 6;
        mResponseData = responseData.clone();
        mstWriteCharacteristic.setValue(mResponseData);
        mGatt.writeCharacteristic(mstWriteCharacteristic);
    }
    /**
     * the device response after App response data was sent
     * checks, if challenge<->response was succsessfull, if so it starts the CALLBACK_SEND_KEYDATA timer to send the keydata
     * @param responseData data the reader responses after app response
     */
    private void deviceResponse(byte[] responseData){
        mHandler.removeCallbacks(rWait_Device_Response);
        lvl = 7;
        if(mstBleDevice.checkDecodeDeviceResponse(responseData)){

            if ((mstBleDevice.getmDeviceType() == 1) || (mstBleDevice.getmDeviceType()==8)){
                if (mstBleDevice.encodeSendKeyData(mSmartkey.getKeydata())) {
                    mHandler.postDelayed(rDelay_DeviceName,100);
                } else {
                    broadcastUpdate(ERROR_DEVICE_RESPONSE);
                    disconnectFromMstDevice();
                }

            }else {
                if (mstBleDevice.encodeSendKeyData(mSmartkey.getKeydata())) {
                    mHandler.postDelayed(rSend_Keydata, 100);
                } else {
                    broadcastUpdate(ERROR_DEVICE_RESPONSE);
                    disconnectFromMstDevice();
                }
            }

        }else{
            broadcastUpdate(ERROR_DEVICE_RESPONSE);
            disconnectFromMstDevice();
        }
    }

    private void notSupportedAuthMode(){
        if(rxMode == RX_MODE_AUTH){
        broadcastUpdate(ERROR_AUTH_FAILED);}
        disconnectFromMstDevice();
    }

    private void publishRoomName(byte[] rxBuf){
        int i;
        byte[] rName  = new byte[32];
        byte[] rLogNo = new byte[16];
        String result;
        mHandler.removeCallbacks(rTimeout_DeviceName);
        mHandler.postDelayed(rSend_Keydata, 100);
        if (rxBuf.length > 16) {
            for (i = 16; (i < rxBuf.length) && (i < 48); i++) {
                   rName[i - 16] = rxBuf[i];
            }
            for (i = 0; i < 16; i++) {
                   rLogNo[i] = rxBuf[i];
            }
            result = MSTUtils.byteArrToTxtString(rName);

            if(result.length() < 1){result = MSTUtils.byteArrToTxtString(rLogNo);
            }
            if(result.length() < 1) result ="Special";
            broadcastUpdate(ACTION_SPECIAL_NAME, result);
        } else {
            for (i = 0; i < rxBuf.length; i++) {
                rLogNo[i] = rxBuf[i];
            }

            result = MSTUtils.byteArrToTxtString(rLogNo);
            if(result.length() < 1) result ="Special";
            broadcastUpdate(ACTION_SPECIAL_NAME, result);
        }
    }

    private void getRoomNameFailed(){
        mHandler.postDelayed(rSend_Keydata, 100);
    }

    private void waitForDeviceResponse(){
        mHandler.postDelayed(rWait_Device_Response, 600);
    }

    // TODO Remove Suppress
    @SuppressLint("MissingPermission")
    private void getReaderDescription(){
        mHandler.postDelayed(rTimeout_DeviceName, 500);
        byte[] tmp = new byte[]{0x00, 0x02, 0x01};
        mstWriteCharacteristic.setValue(tmp);
        mGatt.writeCharacteristic(mstWriteCharacteristic);
    }

    /**
     * sends keydata block
     * @param blkcnt of keydata
     */
    // TODO Remove Suppress
    @SuppressLint("MissingPermission")
    private void sendBlock(int blkcnt){
        lvl = 8;
        try{
            mstWriteCharacteristic.setValue(mstBleDevice.getKeydataBlk(blkcnt));
            mGatt.writeCharacteristic(mstWriteCharacteristic);
        }catch(UnknownError error) {
              error.printStackTrace();
        }
    }

    /**
     * decodes the result from device after sendKeyData
     * @param keyResultData data, that comes from the onChange event
     */
    private void decodeKeyResult(byte[] keyResultData){
        mHandler.removeCallbacks(rWait_Key_Message_Data);
        mHandler.postDelayed(rWait_Door_State, 5000);
        lvl = 9;
        if(keyResultData[6] == 0x00) {
            broadcastUpdate(ACTION_OPEN_THE_DOOR);
        }else{
            broadcastUpdate(ACTION_OPEN_THE_DOOR_FAILED,"invalid Key Data");
        }

        if (!mstBleDevice.getmDevicePowerFlag()) {
            if ((keyResultData[15] & 0x08) == 0x08) {
//                broadcastUpdate(ACTION_BATT_STATUS, "OK");
                broadcastUpdate(ACTION_BATT_STATUS,true);
            } else {
//                broadcastUpdate(ACTION_BATT_STATUS, "EMPTY");
                broadcastUpdate(ACTION_BATT_STATUS, false);
            }
        }

    }

    /**
     * starts close connection procedure after reader device sends door close command
     */
    private void doorStateReceived(){
        mHandler.removeCallbacks(rWait_Door_State);
        lvl = 10;
        disconnectFromMstDevice();
    }
    private void authFailed(){
        mHandler.removeCallbacks(rWait_Key_Message_Data);
        if(rxMode == RX_MODE_AUTH){
        broadcastUpdate(ERROR_AUTH_FAILED);}
        disconnectFromMstDevice();
    }
    /**
     * if timeout was send from device...
     * starts close connection procedure after reader device sends door close command
     */
    private void deviceTimeout(){
        disconnectFromMstDevice();
    }
    /**
     * disables Notification of MSTReadCharacteristic and wait 200ms
     *
     */
    // TODO Remove Suppress
    @SuppressLint("MissingPermission")
    private void disconnectFromMstDevice(){
        try {
            mHandler.removeCallbacks(rTimeout_OverallTimeout);
            mHandler.postDelayed(rWait_Disconnect, 2000);
            BluetoothGattDescriptor descriptor = mstReadCharacteristic.getDescriptor(MSTUtils.MSTNotifyDescriptorUUID);
            descriptor.setValue(BluetoothGattDescriptor.DISABLE_NOTIFICATION_VALUE);
            mGatt.writeDescriptor(descriptor);
        }catch(UnknownError error) {
          //  e.printStackTrace();
        }
    }
    /**
     * disconnect from reader device and null the GATT instance
     * ends the complete procedure
     */
    // TODO Remove Suppress
    @SuppressLint("MissingPermission")
    private void closeGatt() {
        mHandler.removeCallbacks(rTimeout_OverallTimeout);

        if (mGatt == null) {
            broadcastUpdate(ACTION_FINISH_OPEN);
            return;
        }
        try {
            mGatt.disconnect();
        }catch(UnknownError error){
         //   error.printStackTrace();
        }
    }
    // TODO Remove Suppress
    @SuppressLint("MissingPermission")
    private void closeGattErr() {
        mHandler.removeCallbacks(rTimeout_OverallTimeout);

        if (mGatt == null) {
            broadcastUpdate(ACTION_FINISH_OPEN);
            return;
        }
        try {
            mGatt.close();
            mGatt = null;
            mstBleDevice = null;
            broadcastUpdate(ACTION_FINISH_OPEN);
        }catch(UnknownError error){
            //   error.printStackTrace();
        }
    }
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
//device Scan
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    /**
     * Callback function of device scan
     * if a device was found during scan, it will be checked for the following attributes
     * because of this, an extended BluetoothDevice class -> MSTBleDevice will be instanceiated
     * the check consists of:
     * Powerflag: 0..battery driven reader device; 1..permanent powered reader device
     * -> in order to this the RSSI will checked, if the app/smartphone is near enough to the reader device
     * -> check of systemcode
     * Readertype: must be special reader
     * or reader number equals one of the rooms which are enabled in the mSmartkey instance
     */

    private int mod(int x, int y)
    {
        int result = x % y;
        return result < 0? result + y : result;
    }

   /*
    private final BluetoothAdapter.LeScanCallback mLeScanCallback = new BluetoothAdapter.LeScanCallback() {
        @Override
        public void onLeScan(final BluetoothDevice device, final int rssi, final byte[] scanRecord) {
            if((device.getName() != null)  &&  (device.getName().length() == 8)) {

                int preferredRSSI;

                if (rxMode < 0) {
                    final MSTBleDevice deviceLe = new MSTBleDevice(device);
                    if (deviceLe.getmDevicePowerFlag()) {
                        preferredRSSI = mBluetoothUtils.getRSSI_12V();
                    } else {
                        preferredRSSI = mBluetoothUtils.getRSSI_Batt();
                    }

                    if (rssi > preferredRSSI) {
                        if (mod(deviceLe.getmDeviceSystemCode(),1024) == mod(mSmartkey.getSystemcode(),1024)) {
                                if (deviceLe.getmDeviceType() != 15) {
                                    if ((deviceLe.getmDeviceType() == 1) || (deviceLe.getmDeviceType() == 8) || ((mSmartkey.Rooms().contains(deviceLe.getmDeviceNumber())) && (deviceLe.getmDeviceType() == 0))) {
                                        scanLeDevice(-1, false);
                                        rxMode = RX_MODE_SCAN;
                                        mstBleDevice = deviceLe;
                                        connectToMstDevice(mstBleDevice);
                                    }
                                }
                        }
                    }
                }
            }
            }
    };*/

    // TODO Remove Suppress
    @SuppressLint("MissingPermission")
    private ScanCallback mleScanCallback =
            new ScanCallback() {
                @Override
                public void onScanResult(int callbackType, ScanResult result) {
                    super.onScanResult(callbackType, result);
                    BluetoothDevice device = result.getDevice();

                    if((device.getName() != null)  &&  (device.getName().length() == 8)) {
//                    if (Objects.equals(device.getName(), uniqueKeyMain)) {
                        Log.e("// device name " + device.getName().toString(), "");

                        int preferredRSSI;

                        if (rxMode < 0) {
                            final MSTBleDevice deviceLe = new MSTBleDevice(device);
                            if (deviceLe.getmDevicePowerFlag()) {
                                preferredRSSI = mBluetoothUtils.getRSSI_12V();
                            } else {
                                preferredRSSI = mBluetoothUtils.getRSSI_Batt();
                            }

                            if (result.getRssi() > preferredRSSI) {
                                if (mod(deviceLe.getmDeviceSystemCode(),1024) == mod(mSmartkey.getSystemcode(),1024)) {
                                    if (deviceLe.getmDeviceType() != 15) {
                                        if ((deviceLe.getmDeviceType() == 1) || (deviceLe.getmDeviceType() == 8) || ((mSmartkey.Rooms().contains(deviceLe.getmDeviceNumber())) && (deviceLe.getmDeviceType() == 0))) {
                                            scanLeDevice(-1, false);
                                            rxMode = RX_MODE_SCAN;
                                            mstBleDevice = deviceLe;
//                                            connectToMstDevice(mstBleDevice);
                                            broadcastUpdate(ACTION_CONNECT_TO, mstBleDevice);
//                                            broadcastUpdate(ACTION_BATT_STATUS);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            };



    /**
     * starts/stops the scanning procedure
     */
    // TODO Remove Suppress
    @SuppressLint("MissingPermission")
    private void scanLeDevice(final int duration, final boolean enable) {
        if (enable) {
            if (mScanning) {
                return;
            }
            // Stops scanning after a pre-defined scan period.
            mHandler.postDelayed(rDiscover_Devices, duration);
            mHandler.postDelayed(rTimeout, 10000);
            mScanning = true;
            //mBluetoothUtils.getBluetoothAdapter().startLeScan(mLeScanCallback);
            mBleScanner.startScan(mleScanCallback);
        } else {
            mScanning = false;
            mHandler.removeCallbacks(rDiscover_Devices);
            //mBluetoothUtils.getBluetoothAdapter().stopLeScan(mLeScanCallback);
            mBleScanner.stopScan(mleScanCallback);
        }
    }
    /**
     * stops the scanning procedure
     */
    // TODO Remove Suppress
    @SuppressLint("MissingPermission")
    private void stopLeScan(){
            mScanning = false;
            mHandler.removeCallbacks(rTimeout);
            //mBluetoothUtils.getBluetoothAdapter().stopLeScan(mLeScanCallback);
            mBleScanner.stopScan(mleScanCallback);
    }

//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
//device/GATT Connection and
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    /**
     * checks, if the connecting device is a MST reader device
     * @param device the extended version of a BluetoothDevice instance -> MSTBleDevice
     */
    private void connectToMstDevice(MSTBleDevice device){
        if(device.getmIsLrcCorrect()){
            broadcastUpdate(ACTION_CONNECT_TO, mstBleDevice.getmDeviceNumber());
            connectToDevice(mstBleDevice.getDevice());
        }else{
            //MST check fails
        }
    }
    /**
     * starts connecting to given Bluetooth Device
     * @param device Bluetooth Device
     */

    // TODO Remove Suppress
    @SuppressLint("MissingPermission")
    private void connectToDevice(final BluetoothDevice device){
        if (mGatt == null) {
                mHandler.postDelayed(rTimeout_OverallTimeout, 10000);
                Handler connector = new Handler(Looper.getMainLooper());
                Runnable myRunner = new Runnable() {
                    @Override
                    public void run() {
                        mGatt = device.connectGatt(mContext, false, gattCallback);
                    }
                };
                connector.post(myRunner);
            }else{
                //Bluetooth Accessoire not available..
            }
    }
    /**
     * callback functions of the ble connection
     */
    private final BluetoothGattCallback gattCallback;

    {
        gattCallback = new BluetoothGattCallback() {
            /**
             * connection status
             * if the connection is established, the discovery of the services of the connected device will be started
             *
             * @param gatt     Bluetooth Device, which is connected
             * @param status   connection status
             * @param newState the new state after change
             */
            @Override
            // TODO Remove Suppress
            @SuppressLint("MissingPermission")
            public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
                switch (newState) {
                    case BluetoothProfile.STATE_CONNECTED:
                        mHandler.postDelayed(rTimeout_disc_service, 3000);
                        mHandler.removeCallbacks(rTimeout);
                        // Attempts to discover services after successful connection.
                        lvl = 1;
                        scanLeDevice(-1, false);
                        gatt.discoverServices();
                        break;
                    case BluetoothProfile.STATE_DISCONNECTED:
                        if (rxMode != RX_MODE_KEY_EX) {
                            mHandler.removeCallbacks(rTimeout_OverallTimeout);
                            mHandler.removeCallbacks(rTimeout);
                            mHandler.removeCallbacks(rWait_Disconnect);
                            try {
                                if (mGatt != null) {
                                    mGatt.close();
                                    mGatt = null;
                                }
                                mstBleDevice = null;
                                broadcastUpdate(ACTION_FINISH_OPEN);
                            }catch(UnknownError error){
                                //   error.printStackTrace();
                            }
                        }else{
                            rxMode = RX_MODE_KEY;
                        }
                        break;
                    case BluetoothProfile.STATE_DISCONNECTING:
                        break;
                }
            }

            /**
             * if all necessary services and the needed characteristics on this service are found,
             * the communication can be started...
             *
             * @param gatt   connected Bluetooth Device
             * @param status status
             */
            @Override
            public void onServicesDiscovered(BluetoothGatt gatt, int status) {
                BluetoothGattService mstService;
                mstReadCharacteristic = null;
                mstWriteCharacteristic = null;
                lvl = 3;
                mHandler.removeCallbacks(rTimeout_disc_service);
                if (gatt == null) {
                    return;
                }

                mstService = gatt.getService(MSTUtils.MSTPrivateServiceUUID);
                if (mstService == null) {
                    return;
                }

                mstReadCharacteristic = mstService.getCharacteristic(MSTUtils.MSTReadCharacteristicUUID);
                if ((mstReadCharacteristic == null)) {
                    return;
                }

                mstWriteCharacteristic = mstService.getCharacteristic(MSTUtils.MSTWriteCharacteristicUUID);
                if ((mstWriteCharacteristic == null)) {
                    return;
                }
                mHandler.postDelayed(rConnect_Discover_Services, 300);
            }

            /**
             * callback after a characteristic was written
             * this fires after the keydata blocks were written
             *
             * @param gatt           connected Bluetooth Device
             * @param characteristic written characteristic
             * @param status         status
             */
            @Override
            public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
                try {
                    if (status == BluetoothGatt.GATT_SUCCESS) {
                        byte [] value = characteristic.getValue();
                        if (value == mResponseData) {
                            waitForDeviceResponse();
                        }
                        if (characteristic.getValue() == mstBleDevice.getKeydataBlk(0)) {
                            mHandler.postDelayed(rSend_Keydata2, 50);
                        }
                        if (characteristic.getValue() == mstBleDevice.getKeydataBlk(1)) {
                            mHandler.postDelayed(rSend_Keydata3, 50);
                        }
                        if (characteristic.getValue() == mstBleDevice.getKeydataBlk(2)) {
                            if (mstBleDevice.getKeydataBlk(3).length>16) {
                                mHandler.postDelayed(rSend_Keydata4, 50);
                            } else {
                                mHandler.postDelayed(rWait_Key_Message_Data, 1500);
                            }
                        }
                        if (characteristic.getValue() == mstBleDevice.getKeydataBlk(3)) {
                            mHandler.postDelayed(rWait_Key_Message_Data, 1500);
                        }
                    }
                } catch (UnknownError error) {
                    //write failed
                }

            }

            /**
             * callback after a characteristic was changed
             * see method checkCharacteristicChange
             *
             * @param gatt           connected Bluetooth Device
             * @param characteristic changed characteristic
             */
            @Override
            public void onCharacteristicChanged(BluetoothGatt gatt,
                                                BluetoothGattCharacteristic characteristic) {
                final byte[] data = characteristic.getValue();
                checkCharacteristicChange(data);
            }

            /**
             * callback after a notification was enabled/disabled
             *
             * @param gatt       connected Bluetooth Device
             * @param descriptor changed characteristic
             * @param status     status
             */
            @Override
            public void onDescriptorWrite(BluetoothGatt gatt, BluetoothGattDescriptor descriptor, int status) {
                if(lvl < 5) {
                    mResponseData = null;
                }
            }
        };
    }

//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
//rest
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    /**
     * method to start door opening process
     * the status can be checked by instanciating a local broadcast manager on calling context
     */
    public void mstOpenDoor(String uniqueKey){
        broadcastUpdate(ACTION_START_OPEN);
        lvl = 0;
        scanLeDevice(5000, true);
        uniqueKeyMain = uniqueKey;
    }
    /**
     * sends an information update to calling context
     * @param action the corresponding message
     */
    private void broadcastUpdate(final String action) {
        final Intent intent = new Intent(action);
        LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent);
    }
    /**
     * sends an information update to calling context with extra data
     * here: sends back the roomnumber of reader device, which the app is connected
     * if -1, the reader device is a special reader
     * @param action the corresponding message
     * @param rn the physical room number
     */
    private void broadcastUpdate(final String action,
                                 final int rn) {
        final Intent intent = new Intent(action);
        intent.putExtra(EXTRA_DATA, rn);
        LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent);
    }
    /**
     * sends an information update to calling context with extra data
     * here: sends back the roomnumber of reader device, which the app is connected
     * if -1, the reader device is a special reader
     * @param action the corresponding message
     * @param data reason, why card is invalid
     */
    private void broadcastUpdate(final String action,
                                 final String data) {
        final Intent intent = new Intent(action);
        intent.putExtra(EXTRA_DATA, data);
        LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent);
    }

     private void broadcastUpdate(final String action,
                                 final boolean dataBattery) {
        final Intent intent = new Intent(action);
        intent.putExtra("dataBattery", dataBattery);
        LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent);
    }



    private void broadcastUpdate(final String action, final MSTBleDevice data) {

        final Intent ii = new Intent(action);
        ii.putExtra("data", data);
        LocalBroadcastManager.getInstance(mContext).sendBroadcast(ii);

    }

    public void openDoor(MSTBleDevice repeatUnLock) {
        MSTBleDevice bleDevice;
        if (repeatUnLock != null){
            bleDevice = repeatUnLock;
            mstBleDevice = bleDevice;
        }else {
            bleDevice = mstBleDevice;
        }
        if (bleDevice.getmIsLrcCorrect()) {
            broadcastUpdate(ACTION_OPEN_THE_DOOR, bleDevice.getmDeviceNumber());
            connectToDevice(bleDevice.getDevice());
        }

    }

    public void openDoorAddLock() {
        connectToDevice(mstBleDevice.getDevice());
    }


}

