package core.http.client.test

import core.http.client.HttpError
import core.http.client.HttpRequest
import core.http.client.HttpResponse
import core.http.client.test.models.TestRequestResponse
import core.monitoring.common.test.CLILogger
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlin.test.assertNotEquals

internal class MockHttpClientTest {

    @Test
    fun getRequestSuccess() = runBlocking {
        val bodyStr = Json.encodeToString(MockModel.Empty)
        val client = MockHttpClient(CLILogger(""))
        val request = HttpRequest(
            url = "www.mock.com",
            body = null,
            method = HttpRequest.Method.GET,
            headers = listOf()
        )
        val mockResponse = HttpResponse(
            request = request,
            responseHeaders = mapOf(),
            responseCode = 200,
            body = bodyStr
        )
        client.setup(
            TestRequestResponse(
                request = request,
                response = mockResponse
            )
        )
        val response = client.request(request)

        assertNotEquals(
            mockResponse,
            response.copy(responseCode = 400)
        )
        assertEquals(mockResponse, response)
    }

    @Test
    fun throwCorrectException(): Unit = runBlocking {
        try {
            val client = MockHttpClient(CLILogger(""))
            val request = HttpRequest(
                url = "www.mock.com",
                body = null,
                method = HttpRequest.Method.GET,
                headers = listOf()
            )
            client.request(request)
        } catch (ex: Exception) {
            assertIs<HttpError>(ex)
        }
    }

    @Serializable
    internal data class MockModel(val data: String) {
        companion object {
            val Empty = MockModel("Hello Mock!")
        }
    }
}