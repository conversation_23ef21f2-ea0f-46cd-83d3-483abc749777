plugins {
    id("common.library")
    id("jvm.target.library")
    id("android.target.library")
    // id("ios.target.library")
}

commonDependencies {
    api(project(":core-http-client-common"))
    implementation(project(":core-monitoring-common"))

    implementation(libs.ktor.client.core)
    implementation(libs.ktor.client.content.negotiation)
    implementation(libs.ktor.client.encoding)
    implementation(libs.ktor.serialization.kotlinx.json)
}

androidDependencies {
    implementation(libs.ktor.client.android)
}

commonTestDependencies {
    implementation(libs.ktor.client.mock)
}