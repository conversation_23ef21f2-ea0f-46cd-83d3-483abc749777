package core.http.download

import android.app.DownloadManager
import android.content.Context
import android.content.Context.DOWNLOAD_SERVICE
import android.net.Uri
import core.http.download.models.Download
import core.http.download.models.DownloadState
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.log
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.io.File

class AndroidDownloader(
    context: Context,
    private val logger: Logger
) : Downloader {

    private val manager = context.getSystemService(DOWNLOAD_SERVICE) as DownloadManager

    override fun enqueue(url: String, title: String, savePath: String): DownloadId = logger.log {
        val request = DownloadManager.Request(Uri.parse(url))
            .setNotificationVisibility(
                DownloadManager.Request.VISIBILITY_VISIBLE
            )
            .setDestinationUri(Uri.fromFile(File(savePath)))
            .setTitle(title)
            .setAllowedOverMetered(true)

        manager.enqueue(request)
    }

    override fun cancel(id: DownloadId) {
        manager.remove(id)
    }

    override fun listen(id: DownloadId): Flow<Download> = flow {
        emit(getDownload(id, DownloadState.Pending))
        delay(1000)

        while (!isFinished(id)) {
            if (isInProgress(id)) {
                emit(getDownload(id, DownloadState.InProgress))
            }
            delay(1000)
        }

        emit(getDownload(id, DownloadState.Finished))
    }

    private fun getDownload(id: DownloadId, state: DownloadState): Download {
        val total = getTotalBytes(id)
        val downloaded = getDownloadedBytes(id)
        val progress = getProgress(id)

        return Download(state = state, downloadedBytes = downloaded, totalBytes = total, id = id, progress = progress)
    }

    override fun getProgress(id: DownloadId): Double = logger.log {
        val total = getTotalBytes(id)
        val downloaded by lazy { getDownloadedBytes(id) }

        if (total <= 0) return@log 0.0

        return@log (downloaded * 100.0) / total
    }

    override fun getDownloadedBytes(id: DownloadId): Long = logger.log {
        val cursor = manager.query(DownloadManager.Query().setFilterById(id))
        val column = DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR
        val columnIndex = cursor.getColumnIndex(column)
        cursor.moveToFirst()
        val downloaded = if (columnIndex != -1) cursor.getLong(columnIndex) else 0

        cursor.close()

        return@log downloaded
    }

    override fun getTotalBytes(id: DownloadId): Long = logger.log {
        val cursor = manager.query(DownloadManager.Query().setFilterById(id))
        val column = DownloadManager.COLUMN_TOTAL_SIZE_BYTES
        val columnIndex = cursor.getColumnIndex(column)
        cursor.moveToFirst()
        val size = if (columnIndex != -1) cursor.getLong(columnIndex) else 0

        cursor.close()

        return@log size
    }

    override fun isInProgress(id: DownloadId): Boolean = logger.log {
        val cursor = manager.query(DownloadManager.Query().setFilterById(id))
        val column = DownloadManager.COLUMN_STATUS
        val columnIndex = cursor.getColumnIndex(column)
        cursor.moveToFirst()
        val check = if (columnIndex != -1) cursor.getInt(columnIndex) == DownloadManager.STATUS_RUNNING else false

        cursor.close()

        return@log check
    }

    override fun isPending(id: DownloadId): Boolean = logger.log {
        val cursor = manager.query(DownloadManager.Query().setFilterById(id))
        val column = DownloadManager.COLUMN_STATUS
        val columnIndex = cursor.getColumnIndex(column)
        cursor.moveToFirst()
        val check = if (columnIndex != -1) cursor.getInt(columnIndex) == DownloadManager.STATUS_PENDING else false

        cursor.close()

        return@log check
    }

    override fun isFinished(id: DownloadId): Boolean = logger.log {
        val cursor = manager.query(DownloadManager.Query().setFilterById(id))
        val column = DownloadManager.COLUMN_STATUS
        val index = cursor.getColumnIndex(column)
        cursor.moveToFirst()
        val check = if (index != -1) cursor.getInt(index) == DownloadManager.STATUS_SUCCESSFUL else false

        cursor.close()

        return@log check
    }
}