package core.common.encryption

import java.io.UnsupportedEncodingException
import java.security.InvalidAlgorithmParameterException
import java.security.InvalidKeyException
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.security.SecureRandom
import java.util.Arrays
import java.util.Random
import javax.crypto.BadPaddingException
import javax.crypto.Cipher
import javax.crypto.IllegalBlockSizeException
import javax.crypto.NoSuchPaddingException
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi
import kotlin.math.min

internal object AESHelper {
    var KEY_SIZE = 256
    var IV_SIZE = 128
    var HASH_CIPHER = "AES/CBC/PKCS7Padding"
    var AES = "AES"
    var CHARSET_TYPE = "UTF-8"
    var KDF_DIGEST = "MD5"
    var APPEND = "Salted__"

    @OptIn(ExperimentalEncodingApi::class)
    @Throws(
        UnsupportedEncodingException::class,
        NoSuchAlgorithmException::class,
        NoSuchPaddingException::class,
        InvalidAlgorithmParameterException::class,
        InvalidKeyException::class,
        BadPaddingException::class,
        IllegalBlockSizeException::class
    )
    fun encrypt(password: String, plainText: String): String {
        val saltBytes = generateSalt(8)
        val key = ByteArray(KEY_SIZE / 8)
        val iv = ByteArray(IV_SIZE / 8)
        EvpKDF(password.toByteArray(charset(CHARSET_TYPE)), KEY_SIZE, IV_SIZE, saltBytes, key, iv)
        val keyS: SecretKey = SecretKeySpec(key, AES)
        val cipher = Cipher.getInstance(HASH_CIPHER)
        val ivSpec = IvParameterSpec(iv)
        cipher.init(Cipher.ENCRYPT_MODE, keyS, ivSpec)
        val cipherText = cipher.doFinal(plainText.toByteArray(charset(CHARSET_TYPE)))

        // Thanks kientux for this: https://gist.github.com/kientux/bb48259c6f2133e628ad
        // Create CryptoJS-like encrypted !
        val sBytes = APPEND.toByteArray(charset(CHARSET_TYPE))
        val b = ByteArray(sBytes.size + saltBytes.size + cipherText.size)
        System.arraycopy(sBytes, 0, b, 0, sBytes.size)
        System.arraycopy(saltBytes, 0, b, sBytes.size, saltBytes.size)
        System.arraycopy(cipherText, 0, b, sBytes.size + saltBytes.size, cipherText.size)
        val bEncode = Base64.encodeToByteArray(b)
        return String(bEncode)
    }

    @OptIn(ExperimentalEncodingApi::class)
    fun decrypt(password: String, cipherText: String?): String? {
        return try {
            val ctBytes =
                Base64.decode(cipherText?.toByteArray(charset(CHARSET_TYPE)) ?: byteArrayOf())
            val saltBytes = Arrays.copyOfRange(ctBytes, 8, 16)
            val ciphertextBytes = Arrays.copyOfRange(ctBytes, 16, ctBytes.size)
            val key = ByteArray(KEY_SIZE / 8)
            val iv = ByteArray(IV_SIZE / 8)
            EvpKDF(
                password.toByteArray(charset(CHARSET_TYPE)),
                KEY_SIZE,
                IV_SIZE,
                saltBytes,
                key,
                iv
            )
            val cipher = Cipher.getInstance(HASH_CIPHER)
            val keyS: SecretKey = SecretKeySpec(key, AES)
            cipher.init(Cipher.DECRYPT_MODE, keyS, IvParameterSpec(iv))
            val plainText = cipher.doFinal(ciphertextBytes)
            String(plainText)
        } catch (e: Exception) {
            null
        }
    }

    @Throws(NoSuchAlgorithmException::class)
    private fun EvpKDF(
        password: ByteArray,
        keySize: Int,
        ivSize: Int,
        salt: ByteArray,
        resultKey: ByteArray,
        resultIv: ByteArray
    ): ByteArray {
        return EvpKDF(password, keySize, ivSize, salt, 1, KDF_DIGEST, resultKey, resultIv)
    }

    @Throws(NoSuchAlgorithmException::class)
    private fun EvpKDF(
        password: ByteArray,
        keySize: Int,
        ivSize: Int,
        salt: ByteArray,
        iterations: Int,
        hashAlgorithm: String,
        resultKey: ByteArray,
        resultIv: ByteArray
    ): ByteArray {
        var keySize = keySize
        var ivSize = ivSize
        keySize = keySize / 32
        ivSize = ivSize / 32
        val targetKeySize = keySize + ivSize
        val derivedBytes = ByteArray(targetKeySize * 4)
        var numberOfDerivedWords = 0
        var block: ByteArray? = null
        val hasher = MessageDigest.getInstance(hashAlgorithm)
        while (numberOfDerivedWords < targetKeySize) {
            if (block != null) {
                hasher.update(block)
            }
            hasher.update(password)
            block = hasher.digest(salt)
            hasher.reset()

            // Iterations
            for (i in 1 until iterations) {
                block = hasher.digest(block)
                hasher.reset()
            }
            System.arraycopy(
                block,
                0,
                derivedBytes,
                numberOfDerivedWords * 4,
                min(
                    block!!.size.toDouble(),
                    ((targetKeySize - numberOfDerivedWords) * 4).toDouble()
                ).toInt()
            )
            numberOfDerivedWords += block.size / 4
        }
        System.arraycopy(derivedBytes, 0, resultKey, 0, keySize * 4)
        System.arraycopy(derivedBytes, keySize * 4, resultIv, 0, ivSize * 4)
        return derivedBytes // key + iv
    }

    private fun generateSalt(length: Int): ByteArray {
        val r: Random = SecureRandom()
        val salt = ByteArray(length)
        r.nextBytes(salt)
        return salt
    }
}