package core.common.encryption

import java.nio.charset.StandardCharsets
import java.security.DigestException
import java.security.MessageDigest
import java.util.Arrays
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

actual object Encryption {

    actual fun decrypt(cipher: String?, secretKey: String): String? {
        return try {
            var cipherData = ByteArray(0)
            cipherData = Base64.getDecoder().decode(cipher)
            val saltData = cipherData.copyOfRange(8, 16)
            val md5: MessageDigest = MessageDigest.getInstance("MD5")
            val keyAndIV = generateKeyAndIV(
                32,
                16,
                1,
                saltData,
                secretKey.toByteArray(
                    StandardCharsets.UTF_8
                ),
                md5
            )
            val key = SecretKeySpec(keyAndIV[0], "AES")
            val iv = IvParameterSpec(keyAndIV[1])
            val encrypted = cipherData.copyOfRange(16, cipherData.size)
            val aesCBC = Cipher.getInstance("AES/CBC/PKCS5Padding")
            aesCBC.init(Cipher.DECRYPT_MODE, key, iv)
            val decryptedData = aesCBC.doFinal(encrypted)
            val decryptedText = String(decryptedData, StandardCharsets.UTF_8)
            decryptedText
        } catch (e: java.lang.Exception) {
            return AESHelper.decrypt(secretKey, cipher)
//                throw RuntimeException(e)
        }
    }

    private fun generateKeyAndIV(
        keyLength: Int,
        ivLength: Int,
        iterations: Int,
        salt: ByteArray?,
        password: ByteArray?,
        md: MessageDigest
    ): Array<ByteArray?> {
        val digestLength = md.digestLength
        val requiredLength =
            (keyLength + ivLength + digestLength - 1) / digestLength * digestLength
        val generatedData = ByteArray(requiredLength)
        var generatedLength = 0
        return try {
            md.reset()
            // Repeat process until sufficient data has been generated
            while (generatedLength < keyLength + ivLength) {
                // Digest data (last digest if available, password data, salt if available)
                if (generatedLength > 0) {
                    md.update(
                        generatedData,
                        generatedLength - digestLength,
                        digestLength
                    )
                }
                md.update(password)
                if (salt != null) md.update(salt, 0, 8)
                md.digest(generatedData, generatedLength, digestLength)

                // additional rounds
                for (i in 1 until iterations) {
                    md.update(generatedData, generatedLength, digestLength)
                    md.digest(generatedData, generatedLength, digestLength)
                }
                generatedLength += digestLength
            }

            // Copy key and IV into separate byte arrays
            val result = arrayOfNulls<ByteArray>(2)
            result[0] = generatedData.copyOfRange(0, keyLength)
            if (ivLength > 0) {
                result[1] =
                    generatedData.copyOfRange(keyLength, keyLength + ivLength)
            }
            result
        } catch (e: DigestException) {
            throw java.lang.RuntimeException(e)
        } finally {
            // Clean out temporary data
            Arrays.fill(generatedData, 0.toByte())
        }
    }
}