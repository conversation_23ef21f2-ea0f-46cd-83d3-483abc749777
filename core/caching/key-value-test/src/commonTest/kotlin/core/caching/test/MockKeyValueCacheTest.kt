package core.caching.test

import core.monitoring.common.test.CLILogger
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals

class MockKeyValueCacheTest {

    private lateinit var sample: MockKeyValueCache
    private val name = "name_key"

    @BeforeTest
    fun setUp() {
        sample = MockKeyValueCache(CLILogger(Any()))
    }

    @AfterTest
    fun tearDown() {
        sample.remove(name)
    }

    @Test
    fun setString() {
        assertEquals("", sample.getString(name, ""))
        sample.setString(name, "test")
        assertEquals("test", sample.getString(name, ""))
    }

    @Test
    fun setInt() {
        assertEquals(-1, sample.getInt(name, -1))
        sample.setInt(name, 50)
        assertEquals(50, sample.getInt(name, -1))
    }

    @Test
    fun setLong() {
        assertEquals(-1, sample.getLong(name, -1))
        sample.setLong(name, 50)
        assertEquals(50, sample.getLong(name, -1))
    }

    @Test
    fun setBoolean() {
        assertEquals(false, sample.getBoolean(name, false))
        sample.setBoolean(name, true)
        assertEquals(true, sample.getBoolean(name, false))
    }

    @Test
    fun setFloat() {
        assertEquals(-1F, sample.getFloat(name, -1F))
        sample.setFloat(name, 50F)
        assertEquals(50F, sample.getFloat(name, -1F))
    }

    @Test
    fun remove() {
        assertEquals(-1F, sample.getFloat(name, -1F))
        sample.setFloat(name, 50F)
        assertEquals(50F, sample.getFloat(name, -1F))
        sample.remove(name)
        assertEquals(-1F, sample.getFloat(name, -1F))
    }
}