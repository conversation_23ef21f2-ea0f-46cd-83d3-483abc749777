package core.caching.test

import core.caching.KeyValueCache
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.log
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update

class MockKeyValueCache(private val log: Logger) : KeyValueCache {

    private val storage = MutableStateFlow(mapOf<String, Any>())

    override fun setString(key: String, value: String) = log.log {
        storage.update { it.toMutableMap().apply { put(key, value) } }
    }

    override fun setInt(key: String, value: Int) = log.log {
        storage.update { it.toMutableMap().apply { put(key, value) } }
    }

    override fun setLong(key: String, value: Long) = log.log {
        storage.update { it.toMutableMap().apply { put(key, value) } }
    }

    override fun setBoolean(key: String, value: <PERSON>olean) = log.log {
        storage.update { it.toMutableMap().apply { put(key, value) } }
    }

    override fun setFloat(key: String, value: Float) = log.log {
        storage.update { it.toMutableMap().apply { put(key, value) } }
    }

    override fun getString(key: String, default: String): String = log.log {
        return@log if (storage.value[key] is String) {
            storage.value[key] as String
        } else {
            default
        }
    }

    override fun streamString(key: String, default: String): Flow<String> {
        return storage.map { if (it[key] is String) it[key] as String else default }.distinctUntilChanged()
    }

    override fun getInt(key: String, default: Int): Int = log.log {
        return@log if (storage.value[key] is Int) storage.value[key] as Int else default
    }

    override fun streamInt(key: String, default: Int): Flow<Int> {
        return storage.map { if (it[key] is Int) it[key] as Int else default }.distinctUntilChanged()
    }

    override fun getLong(key: String, default: Long): Long = log.log {
        return@log if (storage.value[key] is Long) {
            storage.value[key] as Long
        } else {
            default
        }
    }

    override fun streamFloat(key: String, default: Float): Flow<Float> {
        return storage.map { if (it[key] is Float) it[key] as Float else default }.distinctUntilChanged()
    }

    override
    fun getBoolean(key: String, default: Boolean): Boolean = log.log {
        return@log if (storage.value[key] is Boolean) {
            storage.value[key] as Boolean
        } else {
            default
        }
    }

    override fun streamBoolean(key: String, default: Boolean): Flow<Boolean> {
        return storage.map { if (it[key] is Boolean) it[key] as Boolean else default }.distinctUntilChanged()
    }

    override fun getFloat(key: String, default: Float): Float = log.log {
        return@log if (storage.value[key] is Float) {
            storage.value[key] as Float
        } else {
            default
        }
    }

    override fun streamLong(key: String, default: Long): Flow<Long> {
        return storage.map { if (it[key] is Long) it[key] as Long else default }.distinctUntilChanged()
    }

    override fun remove(key: String): Unit = log.log {
        storage.update { it.toMutableMap().apply { remove(key) } }
    }

    override fun clear() {
        storage.update { emptyMap() }
    }
}