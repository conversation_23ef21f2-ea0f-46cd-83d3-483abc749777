plugins {
    `kotlin-dsl`
}

java {
    sourceCompatibility = JavaVersion.toVersion(libs.versions.javaCompatibility.get())
    targetCompatibility = JavaVersion.toVersion(libs.versions.javaCompatibility.get())
}

dependencies {
    compileOnly(libs.android.gradle.plugin)
    compileOnly(libs.firebase.performance.gradle.plugin)
    compileOnly(libs.firebase.crashlytics.gradle.plugin)
    compileOnly(libs.kotlin.gradle.plugin)
    compileOnly(libs.moko.resources.gradle.plugin)
    compileOnly(libs.sqldelight.gradle.plugin)
}

gradlePlugin {
    plugins {
        register("keylessAndroidApplication") {
            id = "keyless.android.application"
            implementationClass = "AndroidApplicationConventionPlugin"
        }
        register("keylessAndroidCompose") {
            id = "keyless.android.compose"
            implementationClass = "AndroidComposeConventionPlugin"
        }
        register("keylessAndroidFeature") {
            id = "keyless.android.feature"
            implementationClass = "AndroidFeatureConventionPlugin"
        }
        register("keylessAndroidLibrary") {
            id = "keyless.android.library"
            implementationClass = "AndroidLibraryConventionPlugin"
        }
    }

    plugins {
        register("keylessKotlin") {
            id = "keyless.kotlin"
            implementationClass = "KotlinModuleConventionPlugin"
        }
        register("keylessKotlinxSerialization") {
            id = "kotlinx.serialization"
            implementationClass = "KotlinxSerializationConventionPlugin"
        }
    }

    plugins {
        register("keylessKoin") {
            id = "keyless.koin"
            implementationClass = "KoinConventionPlugin"
        }
        register("keylessSqlDelight") {
            id = "keyless.sqldelight"
            implementationClass = "SqlDelightConventionPlugin"
        }
    }

    plugins {
        register("configValues") {
            id = "config.values"
            implementationClass = "ConfigValuesConventionPlugin"
        }
    }

    plugins {
        register("commonModule") {
            id = "common.module"
            implementationClass = "CommonModuleConventionPlugin"
        }
        register("commonLibrary") {
            id = "common.library"
            implementationClass = "CommonLibraryConventionPlugin"
        }
        register("jvmTargetModule") {
            id = "jvm.target.library"
            implementationClass = "JvmTargetLibraryConventionPlugin"
        }
        register("androidTargetModule") {
            id = "android.target.library"
            implementationClass = "AndroidTargetLibraryConventionPlugin"
        }
        register("iosTargetModule") {
            id = "ios.target.library"
            implementationClass = "IosTargetLibraryConventionPlugin"
        }
    }
}