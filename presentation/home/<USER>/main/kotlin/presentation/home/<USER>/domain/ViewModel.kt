package presentation.home.dashboard.domain

import core.common.status.StatusRepository
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.dashboard.domain.usecases.UseCases

internal class ViewModel(
    private val data: ScreenDataRepository<ScreenData>,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val status: StatusRepository,
    private val useCases: UseCases
) {

    val screenDataStream = data.stream
    val sideEffectsStream = sideEffects.stream

    suspend fun onEvent(event: Event) = when (event) {
        is UserEvent -> onUserAction(event)
        is ScreenEvent -> onScreenEvent(event)
    }

    private suspend fun onUserAction(event: UserEvent) = when (event) {
        is UserEvent.OnLockClick -> useCases.onLockClick.execute(event)
        is UserEvent.ClaimKey -> useCases.claimKey.execute(event)
        is UserEvent.LogService -> useCases.logService.execute(event)
        is UserEvent.OpenUrl -> sideEffects.emit(SideEffect.OpenUrl(event.url))
        is UserEvent.Dial -> sideEffects.emit(SideEffect.Dial(event.phoneNumber))
        is UserEvent.OpenWhatsApp -> sideEffects.emit(SideEffect.OpenWhatsApp(event.phoneNumber))
    }

    private suspend fun onScreenEvent(event: ScreenEvent) = when (event) {
        is ScreenEvent.Init -> useCases.initScreen.execute(event)
        is ScreenEvent.DismissStatus -> status.removeStatus(event.status)
    }
}
