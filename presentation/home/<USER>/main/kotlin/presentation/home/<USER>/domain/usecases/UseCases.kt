package presentation.home.dashboard.domain.usecases

import core.caching.KeyValueCache
import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.permissions.manager.PermissionsManager
import data.keyless.guest.GuestRepository
import data.keyless.home.HomeRepository
import data.keyless.users.UserRepository
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.domain.SideEffect

internal class UseCases(
    private val homeRepository: HomeRepository,
    private val userRepository: UserRepository,
    private val guestRepository: GuestRepository,
    private val permissionsManager: PermissionsManager,
    private val screenData: ScreenDataRepository<ScreenData>,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val logger: Logger,
    private val status: StatusRepository,
    private val settings: KeyValueCache
) {

    private val permissionsUseCase = PermissionsUseCase(
        permissionsManager = permissionsManager,
        logger = logger
    )

    val initScreen = InitScreenUseCase(
        home = homeRepository,
        user = userRepository,
        permissionsUseCase = permissionsUseCase,
        screen = screenData,
        sideEffect = sideEffects,
        logger = logger,
        status = status,
        settings = settings
    )

    val onLockClick = OnLockClickUseCase(
        logger = logger,
        status = status,
        sideEffects = sideEffects
    )

    val claimKey = ClaimKeyUseCase(
        guestRepository = guestRepository,
        logger = logger,
        status = status,
        initScreen = initScreen
    )

    val logService = LogServiceUseCase(
        guestRepository = guestRepository,
        logger = logger,
        status = status
    )
}
