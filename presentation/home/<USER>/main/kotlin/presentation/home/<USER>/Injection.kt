package presentation.home.dashboard

import core.common.status.StatusRepository
import kotlinx.coroutines.flow.combine
import org.koin.core.component.getScopeId
import org.koin.core.module.dsl.viewModel
import org.koin.dsl.module
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.domain.SideEffect
import presentation.home.dashboard.domain.ViewModel
import presentation.home.dashboard.domain.usecases.UseCases
import presentation.home.dashboard.feature.DashboardAndroidViewModel

internal data object DashboardInjectionScope

val dashboardInjection = module {
    scope<DashboardInjectionScope> {
        scoped {
            ScreenDataRepository(initial = ScreenData.empty) {
                combine(it, get<StatusRepository>().stream) { data, status -> data.copy(status = status) }
            }
        }
        scoped { SideEffectsRepository<SideEffect>() }
        scoped {
            UseCases(
                userRepository = get(),
                guestRepository = get(),
                permissionsManager = get(),
                screenData = get(),
                sideEffects = get(),
                logger = get(),
                status = get(),
                homeRepository = get(),
                settings = get()
            )
        }
        scoped { ViewModel(data = get(), sideEffects = get(), status = get(), useCases = get()) }
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<DashboardInjectionScope>(DashboardInjectionScope.getScopeId())
        DashboardAndroidViewModel(
            domain = scope.get(),
            dispatchers = get(),
            errorHandler = get(),
            onClear = { scope.close() }
        )
    }
}
