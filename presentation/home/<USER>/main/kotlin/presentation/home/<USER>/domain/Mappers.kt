package presentation.home.dashboard.domain

import data.common.preferences.Preferences
import data.keyless.guest.ClaimKeyRequest
import data.keyless.guest.LogServicePostRequest
import data.keyless.home.UserHomePostRequest
import data.keyless.users.CheckUserRequest

internal fun UserEvent.ClaimKey.request() = ClaimKeyRequest(bookingNumber)

internal fun ScreenEvent.Init.checkUserRequest() = CheckUserRequest(
    uid = Preferences.uuid.get(),
    isAdmin = Preferences.isAdminLogin()
)

internal fun UserEvent.LogService.request() = LogServicePostRequest(service)

internal fun ScreenEvent.Init.userHomeRequest() = UserHomePostRequest(
    uid = Preferences.uuid.get(),
    isAdmin = Preferences.isAdminLogin()
)