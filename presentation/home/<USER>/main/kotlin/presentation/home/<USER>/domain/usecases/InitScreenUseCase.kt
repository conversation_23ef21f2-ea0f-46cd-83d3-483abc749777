package presentation.home.dashboard.domain.usecases

import core.caching.KeyValueCache
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.lock.iseo.IseoHelper
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.isAfter
import data.common.lastServerTime
import data.common.localDate
import data.common.preferences.Preferences
import data.keyless.home.HomeRepository
import data.keyless.users.UserRepository
import data.keyless.users.models.CheckUserResponse
import data.keyless.users.models.IseoDetailsResponse
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.domain.ScreenEvent
import presentation.home.dashboard.domain.SideEffect
import presentation.home.dashboard.domain.checkUserRequest
import presentation.home.dashboard.domain.userHomeRequest
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers

internal class InitScreenUseCase(
    private val home: HomeRepository,
    private val user: UserRepository,
    private val permissionsUseCase: PermissionsUseCase,
    private val screen: ScreenDataRepository<ScreenData>,
    private val sideEffect: SideEffectsRepository<SideEffect>,
    private val logger: Logger,
    private val status: StatusRepository,
    private val settings: KeyValueCache
) {

    init {
        // Forward permission side effects to the main side effect repository
        CoroutineScope(Dispatchers.Main).launch {
            permissionsUseCase.sideEffect.collect { permissionSideEffect ->
                sideEffect.emit(permissionSideEffect)
            }
        }
    }

    suspend fun execute(event: ScreenEvent.Init) = logger.async {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) throw result.error
    }

    private suspend fun job(event: ScreenEvent.Init) = logger.async {
        // Check permissions first before proceeding with initialization
        val permissionsGranted = permissionsUseCase.checkAllRequiredPermissions()

        if (permissionsGranted) {
            checkUser(event)
            fetchLocks(event)
            fetchIseoDetails()
        }
        // If permissions are not granted, the PermissionsUseCase will emit appropriate side effects
    }

    private suspend fun checkUser(event: ScreenEvent.Init) = logger.async {
        val response = user.check(event.checkUserRequest())
        handleCheckUserSuccess(response)
    }

    private suspend fun handleCheckUserSuccess(response: CheckUserResponse) = logger.async {
        Preferences.timeZoneName.set(response.timezoneName)
        Preferences.timeZoneOffset.set(response.timezone)
        screen.update { it.copy(userCheck = response) }

        val dueDate = response.dueDateInstant?.localDate ?: return@async
        val serverDate = lastServerTime()?.localDate ?: return@async

        if (dueDate.isAfter(serverDate)) sideEffect.emit(SideEffect.NavToChangePassword)
    }

    private suspend fun fetchLocks(event: ScreenEvent.Init) = logger.async {
        val response = home.fetch(event.userHomeRequest())
        screen.update { it.copy(userHomeResponse = response) }
    }

    private suspend fun fetchIseoDetails() = logger.async {
        val response = user.iseoDetails()
        sideEffect.emit(SideEffect.HandleIseoDetails(response))
    }

    suspend fun handlePermissionResult(permissions: Array<String>, grantResults: IntArray) = logger.async {
        val allGranted = grantResults.all { it == android.content.pm.PackageManager.PERMISSION_GRANTED }

        if (allGranted) {
            // Permissions granted, proceed with initialization
            val event = ScreenEvent.Init
            checkUser(event)
            fetchLocks(event)
            fetchIseoDetails()
        } else {
            // Permissions denied, show appropriate dialog
            permissionsUseCase.openAppSettings()
        }
    }
}