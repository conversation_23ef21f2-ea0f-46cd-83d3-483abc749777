package presentation.home.dashboard.feature

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import data.common.preferences.Preferences
import keyless.presentation.common.R
import kotlinx.coroutines.flow.map
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import presentation.common.feature.state.DerivedState
import presentation.common.feature.state.ItemState
import presentation.common.feature.state.MutableItemState
import presentation.common.feature.state.StringState
import presentation.home.dashboard.domain.ScreenData

internal class StateHolder(
    val data: DerivedState<ScreenData>,
    val ui: UIState = UIState(data = data),
)

internal class UIState(
    val claimKey: MutableItemState<ClaimKeyState> = MutableItemState(ClaimKeyState.None),
    val groceriesDialog: MutableItemState<GroceriesDialogState> = MutableItemState(GroceriesDialogState.None),
    val callBottomSheet: MutableItemState<CallBottomSheetState> = MutableItemState(CallBottomSheetState.None),
    data: ItemState<ScreenData>
) {
    val changePassword = DerivedState(ChangePasswordState.None, data.stream.map { it.changePasswordState() })
    val locks = DerivedState(emptyList(), data.stream.map { it.userHomeResponse?.locks ?: emptyList() })
    val notificationCount = DerivedState(0, data.stream.map { it.userHomeResponse?.totalUnreadNotification ?: 0 })

    @Composable
    fun welcomeText(): String {
        val hour = currentHour()
        val firstName = getFirstName()
        val res = guestWelcomeResource(hour, firstName)
        return if (firstName.isNotBlank()) stringResource(res) + " " + firstName else stringResource(res)
    }

    private fun getFirstName(): String = Preferences.firstName.get().split(" ").firstOrNull() ?: ""
    private fun currentHour(): Int = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).hour
}

internal sealed interface ChangePasswordState {
    object None : ChangePasswordState
    data class Show(val dueDate: String) : ChangePasswordState
}

internal sealed interface ClaimKeyState {
    object None : ClaimKeyState
    data class Show(val retrievalCode: StringState = StringState("")) : ClaimKeyState
}

internal sealed interface GroceriesDialogState {
    object None : GroceriesDialogState
    data class Show(val type: Type) : GroceriesDialogState

    enum class Type { Groceries, Taxi }
}

internal sealed interface CallBottomSheetState {
    object None : CallBottomSheetState
    data class Show(val type: Type) : CallBottomSheetState {

        val title @Composable get() = stringResource(
            when (type) {
                Type.Emergency_Services -> R.string.emergency_services
                Type.Home_Health_Care -> R.string.home_health_care
            }
        )
    }

    enum class Type { Emergency_Services, Home_Health_Care }
}