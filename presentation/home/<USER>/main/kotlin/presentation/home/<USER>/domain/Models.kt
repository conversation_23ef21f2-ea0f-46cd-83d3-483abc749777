package presentation.home.dashboard.domain

import core.common.status.Status
import data.keyless.home.LockSummary
import data.keyless.home.UserHomeResponse
import data.keyless.users.CheckUserResponse

data class ScreenData(
    val userHomeResponse: UserHomeResponse?,
    val userCheck: CheckUserResponse?,
    val status: List<Status>
) {
    companion object {
        val empty = ScreenData(userHomeResponse = null, userCheck = null, status = emptyList())
    }
}

sealed interface SideEffect {
    object NavToChangePassword : SideEffect
    data class NavToLockDetails(val lockSummary: LockSummary) : SideEffect
    data class OpenUrl(val url: String) : SideEffect
    data class Dial(val phoneNumber: String) : SideEffect
    data class OpenWhatsApp(val phoneNumber: String) : SideEffect
}

sealed interface Event

sealed interface UserEvent : Event {
    data class OnLockClick(val lockSummary: LockSummary) : UserEvent
    data class ClaimKey(val bookingNumber: String) : UserEvent
    data class LogService(val service: String) : UserEvent
    data class OpenUrl(val url: String) : UserEvent
    data class Dial(val phoneNumber: String) : UserEvent
    data class OpenWhatsApp(val phoneNumber: String) : UserEvent
}

sealed interface ScreenEvent: Event {
    object Init : ScreenEvent
    class DismissStatus(val status: Status) : ScreenEvent
}