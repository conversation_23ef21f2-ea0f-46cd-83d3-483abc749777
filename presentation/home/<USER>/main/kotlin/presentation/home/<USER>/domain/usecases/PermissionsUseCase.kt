package presentation.home.dashboard.domain.usecases

import android.Manifest
import android.os.Build
import core.common.preferences.Preferences
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.permissions.manager.PermissionsManager
import core.permissions.manager.models.Permission
import core.permissions.manager.models.Response
import data.utils.android.CommonValues.Companion.ADMIN
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import presentation.home.dashboard.domain.SideEffect

class PermissionsUseCase(
    private val permissionsManager: PermissionsManager,
    private val logger: Logger
) {

    private val _sideEffect = MutableSharedFlow<SideEffect>()
    val sideEffect: SharedFlow<SideEffect> = _sideEffect.asSharedFlow()

    suspend fun checkLocationPermissions() = logger.async {
        val isLocationGranted = permissionsManager.check(Permission.LocationPrecise)
        
        if (!isLocationGranted) {
            handleLocationPermissionDenied()
        }
        
        return@async isLocationGranted
    }

    suspend fun requestLocationPermissions() = logger.async {
        val permissions = listOf(Permission.LocationPrecise)
        val responses = permissionsManager.request(permissions)
        
        val allGranted = responses.all { it.result == Response.Result.Granted }
        
        if (!allGranted) {
            handleLocationPermissionDenied()
        }
        
        return@async allGranted
    }

    private suspend fun handleLocationPermissionDenied() = logger.async {
        val userRole = Preferences.userRole.get()
        val message = getPermissionMessage(userRole)
        
        _sideEffect.emit(SideEffect.ShowPermissionDialog(message))
    }

    private fun getPermissionMessage(userRole: String): String {
        return if (userRole == ADMIN) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                // For admin users on Android 12+, use general permissions message
                "please_allow_permissions_to_continue"
            } else {
                // For admin users on older Android versions, use location-specific message
                "location_permission_needed"
            }
        } else {
            // For non-admin users, always use location-specific message
            "location_permission_needed"
        }
    }

    suspend fun openAppSettings() = logger.async {
        _sideEffect.emit(SideEffect.OpenAppSettings)
    }

    suspend fun requestPermissionsFromSystem() = logger.async {
        val userRole = Preferences.userRole.get()
        val permissions = getRequiredPermissions(userRole)
        
        _sideEffect.emit(SideEffect.RequestPermissions(permissions))
    }

    private fun getRequiredPermissions(userRole: String): List<String> {
        return if (userRole == ADMIN) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                // Admin users on Android 12+ need multiple permissions
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    listOf(
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.BLUETOOTH_SCAN,
                        Manifest.permission.BLUETOOTH_CONNECT,
                        Manifest.permission.POST_NOTIFICATIONS
                    )
                } else {
                    listOf(
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.BLUETOOTH_SCAN,
                        Manifest.permission.BLUETOOTH_CONNECT
                    )
                }
            } else {
                // Admin users on older Android versions
                listOf(Manifest.permission.ACCESS_FINE_LOCATION)
            }
        } else {
            // Non-admin users
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    listOf(
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.BLUETOOTH_SCAN,
                        Manifest.permission.BLUETOOTH_CONNECT,
                        Manifest.permission.POST_NOTIFICATIONS
                    )
                } else {
                    listOf(
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.BLUETOOTH_SCAN,
                        Manifest.permission.BLUETOOTH_CONNECT
                    )
                }
            } else {
                listOf(Manifest.permission.ACCESS_FINE_LOCATION)
            }
        }
    }

    suspend fun checkAllRequiredPermissions() = logger.async {
        val userRole = Preferences.userRole.get()
        val requiredPermissions = getRequiredPermissionsAsPermissionObjects(userRole)
        
        val allGranted = requiredPermissions.all { permission ->
            permissionsManager.check(permission)
        }
        
        if (!allGranted) {
            handleLocationPermissionDenied()
        }
        
        return@async allGranted
    }

    private fun getRequiredPermissionsAsPermissionObjects(userRole: String): List<Permission> {
        return if (userRole == ADMIN) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                // Admin users on Android 12+ need multiple permissions
                val basePermissions = listOf(
                    Permission.LocationPrecise,
                    Permission.BluetoothConnect
                )
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    basePermissions + Permission.Notification
                } else {
                    basePermissions
                }
            } else {
                // Admin users on older Android versions
                listOf(Permission.LocationPrecise)
            }
        } else {
            // Non-admin users - same as admin for now
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val basePermissions = listOf(
                    Permission.LocationPrecise,
                    Permission.BluetoothConnect
                )
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    basePermissions + Permission.Notification
                } else {
                    basePermissions
                }
            } else {
                listOf(Permission.LocationPrecise)
            }
        }
    }
}
