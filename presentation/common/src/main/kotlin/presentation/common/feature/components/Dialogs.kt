package presentation.common.feature.components

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.TextView
import android.widget.Toast
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.AlertDialog
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.window.DialogProperties
import com.vdx.designertoast.DesignerToast
import keyless.presentation.common.R
import presentation.common.test.CommonTestTags

private fun successDialogWithOkButton(context: Context, message: String, listener: OnActionOK) {
    val dialog = Dialog(context)
    dialog.setContentView(R.layout.layout_sucess_dialog_with_ok)
    dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
    dialog.setCancelable(false)
    dialog.setCanceledOnTouchOutside(false)
    dialog.window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT)
    val tvText = dialog.findViewById<TextView>(R.id.tvText)
    val tvOk = dialog.findViewById<TextView>(R.id.tvOk)
    tvText.text = "$message"
    tvOk.setOnClickListener {
        dialog.dismiss()
        listener.onClickData()
    }
    dialog.show()
}

fun successDialogWithOkButton(context: Context, message: String, callback: () -> Unit) {
    successDialogWithOkButton(context, message, object : OnActionOK {
        override fun onClickData() {
            callback()
        }
    })
}

fun Context.appToast(message: String) {
    DesignerToast.Custom(
        this, message,
        Gravity.BOTTOM, Toast.LENGTH_LONG, R.drawable.bg_toast, 14,
        "#ffffff", R.mipmap.ic_launcher_round, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT
    )
}

@Composable
fun AppToast(
    modifier: Modifier = Modifier,
    text: String
) {
    AppSurface(
        color = androidx.compose.ui.graphics.Color(0xB0313131),
        contentColor = androidx.compose.ui.graphics.Color.White,
        shape = CircleShape
    ) {
        AppRow(
            modifier = modifier,
            arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
            alignment = Alignment.CenterVertically
        ) {
            AppImage(res = R.mipmap.ic_launcher_round)
            AppLabelText(text = text)
        }
    }
}

@Composable
fun AppAlertDialog(
    message: String,
    onConfirm: () -> Unit
) {
    AlertDialog(
        modifier = Modifier.testTag(CommonTestTags.appDialog(message)),
        title = { AppPageTitleText(text = stringResource(R.string.keyless)) },
        onDismissRequest = {  },
        confirmButton = { AppTextButton(text = stringResource(R.string.ok), onClick = onConfirm) },
        text = { AppBodyText(text = message, design = DesignSystem.Text.Body.copy(alignment = TextAlign.Center)) },
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        )
    )
}

class ProgressDialog private constructor() {

    @Volatile
    private var dialog: Dialog? = null

    fun show(context: Context, cancelable: Boolean) {
        if (dialog != null) return
        dialog = Dialog(context)
        dialog!!.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog!!.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog!!.setContentView(R.layout.custom_progress_dailog)
        dialog!!.setCancelable(cancelable)
        dialog!!.setCanceledOnTouchOutside(cancelable)
        dialog!!.show()
    }

    fun hide() {
        runCatching { dialog?.dismiss() }
        dialog = null
    }

    companion object {
        val instance = ProgressDialog()
    }
}

interface OnActionOK {
    fun onClickData()
}