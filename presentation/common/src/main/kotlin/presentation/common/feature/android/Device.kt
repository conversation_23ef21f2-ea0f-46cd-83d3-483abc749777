package presentation.common.feature.android

import android.content.Context
import android.content.Intent
import androidx.core.net.toUri

fun Context.openUrl(url: String) {
    runCatching { startActivity(Intent(Intent.ACTION_VIEW, url.toUri())) }
}

fun Context.dialPhone(phoneNumber: String) {
    runCatching { startActivity(Intent(Intent.ACTION_DIAL, "tel:$phoneNumber".toUri())) }
}

fun Context.whatsApp(smsNumber: String) {
    runCatching { startActivity(Intent(Intent.ACTION_VIEW, "https://api.whatsapp.com/send?phone=$smsNumber".toUri())) }
}