package data.common.http

import core.common.platform.Platform
import core.common.serialization.json
import core.http.client.HttpError
import core.http.client.HttpResponse
import data.error.HttpAuthenticationError
import kotlinx.serialization.Serializable
import kotlinx.serialization.decodeFromString

@Serializable
class ServerResponse(
    val success: Boolean = false,
    val message: String = "",
    val logout: Boolean = false
)

inline fun <reified T> HttpResponse.unwrap(): T {
    val server = json.decodeFromString(ServerResponse.serializer(), this.body)

    if (server.message.lowercase() == "authentication failed.") {
        throw HttpAuthenticationError()
    }

    if (!server.success) {
        val message = server.message.ifBlank { "Error from the server" }
        throw HttpError(
            message = message,
            cause = Exception("Backend response error"),
            callLocation = Platform.executeLocation(),
            responseCode = 400,
            responseCodeDescription = "Bad Request",
            details = mapOf(),
            responseBody = body
        )
    }

    return json.decodeFromString(body)
}

fun String.encodeUrlParameter(): String {
    return this.replace("/", "%2F").replace(" ", "%20").replace("+", "%2B")
}

fun String.decodeUrlParameter(): String {
    return this.replace("%2F", "/").replace("%20", " ").replace("%2B", "+")
}