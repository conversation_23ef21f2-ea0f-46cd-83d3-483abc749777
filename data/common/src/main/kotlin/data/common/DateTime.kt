package data.common

import data.common.preferences.Preferences
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.format.DateTimeComponents
import kotlinx.datetime.format.alternativeParsing
import kotlinx.datetime.format.char
import kotlinx.datetime.format.optional
import kotlinx.datetime.toLocalDateTime

fun Instant.device() = toLocalDateTime(TimeZone.currentSystemDefault())
fun Instant.isAfter(other: Instant) = this > other
fun Instant.isBefore(other: Instant) = this < other

val Instant.localDate get() = toLocalDateTime(TimeZone.currentSystemDefault()).date
fun LocalDate.isAfter(other: LocalDate) = this > other
fun LocalDate.isBefore(other: LocalDate) = this < other

fun now(): Instant = Clock.System.now()
fun lastServerTime() = runCatching { Instant.parse(Preferences.lastOnlineLocksFetchDateTime.get()) }.getOrNull()

fun Instant.Companion.parseFormatted(str: String) : Instant? {
    println("[LOGGY] parsing $str")
    val comp = DateTimeComponents.Format {
        date(LocalDate.Formats.ISO)
        alternativeParsing({ char('t') }) { char('T') }
        hour()
        char(':')
        minute()
        char(':')
        second()
        optional {
            alternativeParsing({ char('+')}) { char('.') }
            secondFraction(1, 9)
        }
    }
    return runCatching { parse(str, format = comp) }.onFailure { it.printStackTrace() }.getOrNull()
}