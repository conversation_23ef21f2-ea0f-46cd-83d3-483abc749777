package data.user.account.models.dtos

import data.user.account.models.UserProfileResponse
import kotlinx.serialization.Serializable

@Serializable
internal data class UserProfileResponseDto(
    val user: UserProfileDto
) {
    fun toModel() = UserProfileResponse(
        email = user.email,
        username = user.username,
        firstName = user.first_name,
        lastName = user.last_name,
        countryCode = user.country_code,
        mobileNumber = user.mobile_number,
        profilePhoto = user.profile_photo,
        contactPersonName = user.contact_person_name ?: ""
    )
}

@Serializable
internal class UserProfileDto private constructor(
    val email: String,
    val username: String,
    val first_name: String,
    val last_name: String,
    val country_code: String,
    val mobile_number: Long,
    val profile_photo: String,
    val contact_person_name: String?
) {
    fun toModel() = UserProfileResponse(
        email = email,
        username = username,
        firstName = first_name,
        lastName = last_name,
        countryCode = country_code,
        mobileNumber = mobile_number,
        profilePhoto = profile_photo,
        contactPersonName = contact_person_name ?: ""
    )
}