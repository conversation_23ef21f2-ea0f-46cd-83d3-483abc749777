package data.user.home.models.dtos

import data.lock.common.lock.models.lock.TimeRange
import kotlinx.serialization.Serializable

@Serializable
internal data class TimeRangeDto(
    val __v: Int = 0,
    val _id: String = "",
    val allowed_days: ArrayList<Int> = ArrayList(),
    val always_open: Boolean = false,
    val created_at: String = "",
    val holidays: Boolean = false,
    val name: String = "",
    val routine_id: String = "",
    val status: Boolean = false,
    val time_slot: TimeSlotDto = TimeSlotDto()
) {
    fun toModel(): TimeRange {
        return TimeRange(
            id = _id,
            allowedDays = allowed_days,
            alwaysOpen = always_open,
            createdAt = created_at,
            holidays = holidays,
            name = name,
            routineId = routine_id,
            status = status,
            startHour = time_slot.start_hour,
            startMin = time_slot.start_min,
            endHour = time_slot.end_hour,
            endMin = time_slot.end_min
        )
    }
}