package data.user.home.models.dtos

import data.lock.common.lock.models.lock.TimeProfileId
import kotlinx.serialization.Serializable

@Serializable
internal data class TimeProfileIdDto(
    val __v: Int = 0,
    val _id: String = "",
    val created_at: String = "",
    val created_by: String = "",
    val iseo_id: Int = 0,
    val name: String = "",
    val status: Boolean = false
) {
    fun toModel(): TimeProfileId {
        return TimeProfileId(
            createdAt = created_at,
            createdBy = created_by,
            iseoId = iseo_id.toLong(),
            name = name,
            status = status,
            id = _id
        )
    }
}