package data.user.home.local

import data.database.queries.UserHomeResponseQueries
import data.lock.common.lock.models.lock.Icon
import data.lock.common.lock.models.lock.Lock
import data.lock.common.lock.models.lock.LockAssignment
import data.lock.common.lock.models.lock.Property
import data.lock.common.lock.models.lock.PropertyDetails
import data.lock.common.lock.models.lock.TimeProfileId
import data.lock.common.lock.models.lock.UserHomeResponse

internal suspend fun UserHomeResponse.insert(queries: UserHomeResponseQueries, userUid: String) {
    queries.transaction {
        insertHomeResponse(userUid, this@insert, queries)
    }
}

private suspend fun insertHomeResponse(
    userUid: String,
    response: UserHomeResponse,
    queries: UserHomeResponseQueries
) {
    queries.insert_home_response(
        user_uid = userUid,
        success = response.success,
        message = response.message,
        assignment_id = response.assignmentId,
        logout = response.logout,
        is_paid = response.isPaid,
        total_unread_notification = response.totalUnreadNotification.toLong()
    )

    response.locks.forEach { lock ->
        insertLock(lock, queries)

        queries.insert_user_home_response_with_lock(
            user_uid = userUid,
            lock_id = lock.id
        )
    }

    response.properties.forEach { property ->
        insertProperty(property, queries)

        queries.insert_user_home_response_with_property(
            user_uid = userUid,
            property_id = property.id
        )
    }
}

private suspend fun insertLock(
    lock: Lock,
    queries: UserHomeResponseQueries
) {
    queries.insert_lock(
        id = lock.id,
        name = lock.name,
        desc = lock.desc,
        image = lock.image,
        status = lock.status.toLong(),
        battery_level = lock.batteryLevel.toLong(),
        privacy_mode = lock.privacyMode,
        privacy_permission = lock.privacyPermission,
        privacy_owner = lock.privacyOwner,
        primary_lock = lock.primary,
        created_at = lock.createdAt,
        access_key = lock.accessKey,
        unique_key = lock.uniqueKey,
        encrypted_key = lock.encryptedKey,
        internal_id = lock.internalId,
        lock_uid = lock.lockUid,
        unit_id = lock.unitId,
        provider = lock.provider,
        time_zone = lock.timeZone,
        assignment_id = lock.assignment.id,
        property_id = lock.property.id,
        privacy = lock.privacy,
        privacy_changed = lock.privacyChanged,
        company_check_in = lock.companyCheckIn,
        check_in = lock.checkIn,
        booking_number = lock.bookingNumber,
        total_checkins = lock.totalCheckins.toLong(),
        owner_id = lock.ownerId,
        firmware_updated = lock.firmwareUpdated,
        passcode_id = lock.passcodeId,
        passcode = lock.passcode,
        firmware_version = lock.firmwareVersion,
        firmware_available_version = lock.firmwareAvailableVersion,
        tedee_lock_id = lock.tedeeLockId
    )

    insertAssignment(lock.assignment, queries)

    insertLockProperty(lockId = lock.id, lock.property, queries)

    lock.icons.forEach { icon ->
        queries.insert_icon(
            id = icon.id,
            name = icon.name,
            created_at = icon.createdAt,
            type = icon.type,
            icon = icon.icon
        )

        queries.insert_lock_with_icon(
            lock_id = lock.id,
            icon_id = icon.id
        )
    }
}

private suspend fun insertAssignment(
    assignment: LockAssignment,
    queries: UserHomeResponseQueries
) {
    queries.insert_assignment(
        id = assignment.id,
        assigned_at = assignment.assignedAt,
        assigned_by = assignment.assignedBy,
        assigned_to = assignment.assignedTo,
        lock_id = assignment.lockId,
        time_profile_id = assignment.timeProfileId.id,
        valid_from = assignment.validFrom,
        valid_to = assignment.validTo,
        status = assignment.status.toLong()
    )

    insertTimeProfile(assignment.timeProfileId, queries)

    assignment.timeRanges.forEach { timeRange ->
        queries.insert_time_range(
            id = timeRange.id,
            allowed_days = timeRange.allowedDays,
            always_open = timeRange.alwaysOpen,
            created_at = timeRange.createdAt,
            holidays = timeRange.holidays,
            name = timeRange.name,
            routine_id = timeRange.routineId,
            status = timeRange.status,
            start_hour = timeRange.startHour.toLong(),
            start_minute = timeRange.startMin.toLong(),
            end_hour = timeRange.endHour.toLong(),
            end_minute = timeRange.endMin.toLong()
        )

        queries.insert_lock_assignment_with_time_range(
            lock_assignment_id = assignment.id,
            time_range_id = timeRange.id
        )
    }
}

private suspend fun insertTimeProfile(
    timeProfile: TimeProfileId,
    queries: UserHomeResponseQueries
) {
    queries.insert_time_profile(
        id = timeProfile.id,
        name = timeProfile.name,
        status = timeProfile.status,
        created_by = timeProfile.createdBy,
        created_at = timeProfile.createdAt,
        iseo_id = timeProfile.iseoId
    )
}

private suspend fun insertIcon(
    icon: Icon,
    queries: UserHomeResponseQueries
) {
    queries.insert_icon(
        id = icon.id,
        name = icon.name,
        created_at = icon.createdAt,
        type = icon.type,
        icon = icon.icon
    )
}

private suspend fun insertProperty(
    property: Property,
    queries: UserHomeResponseQueries
) {
    queries.insert_property(
        id = property.id,
        area = property.area,
        emirate = property.emirate,
        building_name = property.buildingName,
        laundry_number = property.laundryNumber,
        grocery_number = property.groceryNumber,
        maintenance_number = property.maintenanceNumber,
        support_call_number = property.supportCallNumber,
        support_whatsapp_number = property.supportWhatsappNumber,
        manager_id = property.managerId,
        manager_type = property.managerType,
        total_floors = property.totalFloors,
        created_at = property.createdAt,
        icon_id = property.iconId,
        count = property.count.toLong(),
        latitude = property.latitude,
        longitude = property.longitude
    )

    property.icons.forEach { icon ->
        insertIcon(icon, queries)

        queries.insert_property_with_icon(
            property_id = property.id,
            icon_id = icon.id
        )
    }
}

private suspend fun insertLockProperty(
    lockId: String,
    property: PropertyDetails,
    queries: UserHomeResponseQueries
) {
    queries.insert_lock_property(
        lock_id = lockId,
        id = property.id,
        name = property.name,
        area = property.area,
        emirate = property.emirate,
        building_name = property.buildingName,
        floor = property.floor,
        room_number = property.roomNumber,
        apartment_number = property.apartmentNumber,
        laundry_number = property.laundryNumber,
        grocery_number = property.groceryNumber,
        maintenance_number = property.maintenanceNumber,
        support_call_number = property.supportCallNumber,
        support_whatsapp_number = property.supportWhatsappNumber,
        manager_id = property.managerId,
        manager_type = property.managerType,
        total_floors = property.totalFloors,
        created_at = property.createdAt,
        map_id = property.mapId,
        icon_id = property.iconId,
        count = property.count.toLong(),
        latitude = property.latitude,
        longitude = property.longitude
    )
}