package data.user.home.models.dtos

import data.lock.common.lock.models.lock.Lock
import kotlinx.serialization.Serializable

@Serializable
internal data class LockDetailsDto(
    val assignment: LockAssignmentDto = LockAssignmentDto(),
    val lock: LockDto = LockDto(),
    val owner_id: String = "",
    val unit_id: String = "",
    val booking_number: String = "",
    val privacy: Boolean = false,
    val privacy_changed: Boolean = false,
    val companyCheckin: Boolean = false,
    val checkin: Boolean = false,
    val totalCheckins: Int = 0,
    val property_details: PropertyDetailsDto,
    val passcodeId: String = "",
    val passcode: String = ""
) {
    fun toModel(): Lock {
        return Lock(
            id = lock._id,
            assignment = assignment.toModel(),
            ownerId = owner_id,
            unitId = unit_id,
            bookingNumber = booking_number,
            privacy = privacy,
            privacyChanged = privacy_changed,
            companyCheckIn = companyCheckin,
            checkIn = checkin,
            totalCheckins = totalCheckins,
            property = property_details.toModel(),
            accessKey = lock.access_key,
            batteryLevel = lock.battery_level,
            createdAt = lock.createdAt,
            image = lock.image,
            lockUid = lock.lock_uid,
            name = lock.name,
            desc = lock.desc,
            provider = lock.provider,
            status = lock.status,
            uniqueKey = lock.unique_key,
            timeZone = lock.time_zone,
            internalId = lock.internal_id,
            encryptedKey = lock.encrypted_key,
            privacyMode = lock.privacy_mode,
            primary = lock.primary,
            privacyPermission = lock.privacy_permission,
            privacyOwner = lock.privacy_owner,
            icons = lock.icon.map { it.toModel() },
            firmwareUpdated = lock.firmware_updated,
            firmwareVersion = lock.firmwareVersion ?: "",
            passcodeId = passcodeId,
            passcode = passcode,
            firmwareAvailableVersion = lock.firmwareAvailable ?: "",
            tedeeLockId = lock.tedeeLockId ?: ""
        )
    }
}