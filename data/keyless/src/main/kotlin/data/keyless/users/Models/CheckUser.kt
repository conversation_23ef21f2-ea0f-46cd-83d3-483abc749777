package data.keyless.users.models

import android.annotation.SuppressLint
import kotlinx.datetime.Instant
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CheckUserRequest(
    val uid: String,
    @SerialName("is_admin")
    val isAdmin: Boolean
)

@Serializable
data class CheckUserResponse(
    val success: <PERSON>olean = false,
    @SerialName("force_update")
    val forceUpdate: <PERSON>olean = false,
    @SerialName("force_password_require")
    val forcePasswordRequire: Boolean = false,
    @SerialName("due_date")
    val dueDate: String = "",
    val timezone: String = "",
    @SerialName("timezone_name")
    val timezoneName: String = "",
    val message: String = ""
) {
    val dueDateInstant = runCatching { Instant.parse(dueDate) }.getOrNull()
}

@Serializable
data class IseoDetailsResponse(
    val success: <PERSON>olean = false,
    val message: String = "",
    @SerialName("iseo_url")
    val iseoUrl: String = "",
    @SerialName("plant_name")
    val plantName: String = ""
)
