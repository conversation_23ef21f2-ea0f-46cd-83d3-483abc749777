<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="325dp"
    android:height="221dp"
    android:viewportWidth="325"
    android:viewportHeight="221">
  <group>
    <clip-path
        android:pathData="M24.24,126.6l-24.24,-3.06l3.66,-28.98l24.24,3.06z"/>
    <path
        android:pathData="M20.35,126.11C19.93,125.99 19.51,125.88 19.09,125.75C17.2,125.15 15.56,124.11 14.13,122.74C11.78,120.47 10.25,117.7 9.21,114.63C8.11,111.36 7.69,107.98 7.75,104.54C7.76,104.25 7.78,103.97 7.79,103.65L2.59,102.99C5.77,100.52 8.94,98.07 12.08,95.63C14.51,98.76 16.97,101.93 19.44,105.11L14.19,104.44C14.18,104.99 14.15,105.5 14.15,106.01C14.14,109.05 14.52,112.03 15.44,114.93C16.45,118.1 17.95,121 20.34,123.38C21.45,124.47 22.68,125.36 24.09,126.01C24.15,126.04 24.22,126.08 24.29,126.19C23.75,126.22 23.22,126.26 22.69,126.29C22.42,126.31 22.15,126.3 21.89,126.31C21.38,126.24 20.87,126.18 20.35,126.11Z"
        android:fillColor="#ffffff"/>
  </group>
  <path
      android:pathData="M35.87,16.87L32.91,17.71L2.05,80.58L4.16,84.77L96.39,126.5C96.39,126.5 101.89,123.5 110.39,115C118.89,106.5 127.89,87 127.89,87L130.39,61L35.87,16.87Z"
      android:fillColor="#BEA955"/>
  <path
      android:pathData="M35.87,16.87L32.91,17.71L2.05,80.58L4.16,84.77L96.39,126.5C96.39,126.5 101.89,123.5 110.39,115C118.89,106.5 127.89,87 127.89,87L130.39,61L35.87,16.87Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="66.39"
          android:startY="39.5"
          android:endX="29.89"
          android:endY="122.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF78692B"/>
        <item android:offset="1" android:color="#00BEA955"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M314.4,157.97L246.99,125.72L242.49,135.13L309.87,167.36L291.79,204.83L296.93,207.31L308.39,197.5L318.89,181.5L320.39,163L318.84,161.67L319.46,160.39L314.67,158.1L314.44,157.9L314.4,157.97Z"
      android:fillColor="#D9D9D9"
      android:fillAlpha="0.3"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M128.97,83.09L240.36,136.38L235.87,145.78L219.09,137.76L212.82,150.75L210.42,149.59L216.69,136.61L210.1,133.46L203.56,147.01L201.08,145.81L207.61,132.27L187.12,122.46L180.3,136.58L178.02,135.48L184.83,121.37L171.8,115.14L165.07,129.08L162.12,127.66L168.84,113.72L147.53,103.53L141.01,117.05L138.35,115.77L144.88,102.26L139.11,99.5L132.18,113.85L128.77,112.2L135.68,97.86L124.47,92.5L128.97,83.09Z"
      android:fillColor="#D9D9D9"
      android:fillAlpha="0.3"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M222.36,156.44L122.06,108.33L119.77,113.09L220.07,161.2L209.98,182.1L215.12,184.58L227.61,158.7L222.47,156.22L222.36,156.44Z"
      android:fillColor="#D9D9D9"
      android:fillAlpha="0.3"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M209.92,165.93L122.92,124.78L120.66,129.55L207.63,170.69L203.42,179.4L207.1,181.17L213.71,167.48L210.03,165.71L209.92,165.93Z"
      android:fillColor="#D9D9D9"
      android:fillAlpha="0.3"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M108.33,134.45L120.66,108.31"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.42,124.21L115.9,138.02"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M144.16,90.54L133.26,113.65"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.92,94.67L142.02,117.78"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M214.46,184.5L201.98,178.62"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M190.49,173.2L182.15,169.27"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.8,163.91L151.87,154.98"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M140.51,149.63L132.18,145.7"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M108.33,134.45L120.83,140.34"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M123.17,124.56L122.07,124.95"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M160.16,142L177.1,106.07"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M295.73,207.16L279.95,199.72"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M223.92,173.29L279.96,199.72"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.42,124.21L213.41,167.12"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M163.04,137.82L176.93,144.37"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M180.3,137.22L166.41,130.68"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M175.55,149.26L192.49,113.33"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.89,107.81L132.25,113.17"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.52,114.24L166.2,129.18"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M181.59,136.44L213.39,151.44"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M208.6,149.18L227.02,157.87"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M132.5,113.29L132.25,113.17"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M131.61,112.87L133.39,113.71"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M213.06,167.87L212.65,166.76"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.28,106.31L192.18,113.34"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M240.19,135.73L222.59,127.53"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M224.29,128.33L192.49,113.33"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M129.3,83.43L143.15,90.06"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.89,123.5L110.89,127.5"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M145.42,91.13L177.1,106.07"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M204.38,147.19L215.28,124.08"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M213.58,151.53L224.48,128.42"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M206.9,180.94L213.41,167.12"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M318.48,158.93L302.7,151.49"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M246.67,125.07L302.7,151.49"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M214.46,184.51L226.25,159.5"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M223.92,173.3L246.67,125.07"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M295.73,207.16L298.45,206.3"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M319.54,161.58L318.48,158.93"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.94,140.63L127.68,140.51"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M147.63,149.91L147.38,149.79"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M197.47,173.42L197.22,173.3"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.67,164.08L177.54,164.02"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.88,153.38L164.95,153.17L164.91,152.91L164.78,152.71L164.57,152.57L164.36,152.51L164.11,152.54L163.9,152.68L163.77,152.85L163.7,153.1L163.74,153.32L163.87,153.55L164.06,153.66L164.29,153.75L164.54,153.7L164.75,153.59L164.88,153.38Z"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M132.18,145.7L130.43,142.87L129.33,141.59L127.94,140.63"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.83,140.34L120.89,140.22"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.87,154.98L150.11,152.15L149.02,150.88L147.63,149.91"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M140.51,149.63L140.57,149.5"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.8,163.91L170.86,163.78"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M197.22,173.3L195.59,172.84L193.92,172.81L190.49,173.2"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M182.15,169.27L180.4,166.43L179.31,165.16L177.92,164.2"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M190.49,173.2L190.55,173.07"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M201.97,178.62L200.1,175.72L199.01,174.45L197.6,173.48L197.47,173.42"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M201.98,178.62L202.03,178.49"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M132.18,145.7L132.24,145.57"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.43,140.39L125.8,139.93L124.13,139.89L120.83,140.34"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.68,140.51L127.43,140.38"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.86,154.98L151.92,154.85"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M147.12,149.67L145.48,149.21L143.81,149.17L140.51,149.63"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M147.38,149.79L147.12,149.67"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.92,164.2L177.67,164.08"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M182.15,169.27L182.21,169.14"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.54,164.02L175.9,163.55L174.22,163.52L170.8,163.91"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M3.2,84.01L25.39,94.47"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.89,127L25.39,94.47"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M35.2,16.17L57.38,26.63"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M129.89,60.5L57.38,26.63"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M3.2,84.01L1.89,80.37"
      android:strokeLineJoin="round"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M27.39,50.5C35.39,37.3 35.72,22.33 34.89,16.5H29.89L22.89,25.5L13.39,40L6.89,55C5.22,61 1.89,73.2 1.89,74V81.5H4.89L11.89,73C13.72,71 19.39,63.7 27.39,50.5Z"
      android:fillColor="#78692B"/>
  <path
      android:pathData="M3.39,83C30.59,55 37.39,27.5 34.89,16C32.39,14.5 29.89,17.5 29.89,17.5C7.39,40.5 0.06,73.83 1.89,80.5L3.39,83Z"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"/>
  <path
      android:pathData="M115.49,0.42C116.25,0.59 117.01,0.73 117.77,0.92C121.21,1.77 124.23,3.42 126.92,5.69C131.37,9.44 134.4,14.19 136.6,19.52C138.94,25.21 140.07,31.14 140.35,37.27C140.38,37.78 140.38,38.28 140.4,38.85L149.7,39.42C144.34,44.17 139,48.89 133.7,53.59C129.02,48.31 124.29,42.97 119.55,37.61L128.94,38.18C128.9,37.21 128.89,36.3 128.84,35.39C128.5,30 127.47,24.75 125.5,19.7C123.35,14.19 120.35,9.21 115.82,5.28C113.74,3.47 111.44,2.03 108.88,1.03C108.75,0.98 108.63,0.93 108.5,0.74C109.44,0.61 110.38,0.49 111.33,0.37C111.79,0.32 112.27,0.29 112.75,0.26C113.66,0.31 114.57,0.37 115.49,0.42Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M129.89,60C132.22,73.17 128.69,105.1 95.89,127.5"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"/>
  <path
      android:pathData="M318.89,160C327.39,181.5 302.89,208 294.89,207"
      android:strokeWidth="2.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"/>
  <path
      android:pathData="M41.67,48.39C41.61,48.51 41.66,48.66 41.78,48.72C41.9,48.79 42.05,48.74 42.11,48.61L41.67,48.39ZM45.14,31.99C45.06,33.73 44.78,36.64 44.23,39.72C43.68,42.8 42.85,46.02 41.67,48.39L42.11,48.61C43.33,46.18 44.17,42.9 44.72,39.81C45.28,36.71 45.56,33.77 45.64,32.01L45.14,31.99Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M31.23,77.31C31.12,77.4 31.11,77.55 31.2,77.66C31.29,77.77 31.44,77.78 31.55,77.69L31.23,77.31ZM50.64,33.99C50.56,35.71 49.44,43.44 46.47,52.3C43.49,61.16 38.68,71.1 31.23,77.31L31.55,77.69C39.1,71.4 43.96,61.34 46.94,52.45C49.92,43.56 51.06,35.79 51.14,34.01L50.64,33.99Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M93.25,119.79C93.14,119.87 93.11,120.02 93.18,120.14C93.26,120.25 93.41,120.29 93.53,120.21L93.25,119.79ZM120.65,86.43C119.17,91.38 110.7,108.38 93.25,119.79L93.53,120.21C111.08,108.72 119.62,91.62 121.13,86.57L120.65,86.43Z"
      android:fillColor="#ffffff"/>
</vector>
