package data.utils.android.database

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

class Converters {

    @TypeConverter
    fun convertAnswers(value: String?): ArrayList<ModelErrorData>? {
        val listType = object : TypeToken<ArrayList<ModelErrorData>>() {
        }.type
        return Gson().fromJson(value, listType)
    }

    @TypeConverter
    fun fromLatlng(latlong: ArrayList<ModelErrorData>?): String? {
        val gson = Gson()
        return gson.toJson(latlong)
    }

    @TypeConverter
    fun fromList(value: String?): ArrayList<String>? {
        val listType = object : TypeToken<List<String>>() {
        }.type
        return Gson().fromJson(value, listType)
    }

    @TypeConverter
    fun fromListAnswers(allAnswers: ArrayList<String>?): String? {
        val gson = Gson()
        return gson.toJson(allAnswers)
    }
//
//    @TypeConverter
//    fun toPathList(latlong: List<PathModel>?): String? {
//        val gson = Gson()
//        return gson.toJson(latlong)
//    }
// //    @TypeConverter
// //    fun fromLatlngArrayList(value: ArrayList<LatLng>?): String? {
// //        val gson = Gson()
// //        return gson.toJson(value)
// //    }
//    @TypeConverter
//    fun fromLatlngList(value: List<LatLng>?): String? {
//        val gson = Gson()
//        return gson.toJson(value)
//    }
//
//
//    @TypeConverter
//    fun splitString(data:List<SplitDataModel>?): String? {
//        val gson = Gson()
//        return gson.toJson(data)
//    }
//
//    @TypeConverter
//    fun splitModelList(value: String?): List<SplitDataModel>? {
//        val listType = object : TypeToken<List<SplitDataModel>>() {
//        }.type
//        return Gson().fromJson(value, listType)
//    }
}