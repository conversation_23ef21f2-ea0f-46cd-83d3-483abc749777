package data.utils.android.database

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy.Companion.REPLACE
import androidx.room.Query

@Dao
interface DaoAccess {

    @Insert(onConflict = REPLACE)
    fun insertErrors(modelErrorList: ArrayList<ModelErrorData>)

    @Query("SELECT * FROM ModelErrorData WHERE lockInternalId = :internalId AND timeStampDate >= :timestamp48HoursAgo")
    fun getErrorList(internalId: String, timestamp48HoursAgo: Long): List<ModelErrorData>

    @Query("DELETE FROM ModelErrorData")
    fun deleteAllData()
}