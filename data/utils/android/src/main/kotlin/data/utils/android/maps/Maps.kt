package data.utils.android.maps

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.net.Uri
import androidx.core.content.ContextCompat
import com.google.android.gms.maps.model.BitmapDescriptor
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions
import data.network.android.models.IconModel
import keyless.data.utils.android.R

internal object Maps {

    fun directionStockMap(context: Context, lat: String?, lng: String?) {
        val strUri = "http://maps.google.com/maps?q=loc:$lat,$lng (Location)"
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(strUri))
        context.startActivity(intent)
    }

    fun addCustomMarker(
        context: Context,
        latLng: LatLng,
        iconList: ArrayList<IconModel>?
    ): MarkerOptions {
        val markerOptions = MarkerOptions().position(latLng)
        var icon = R.drawable.marker_other
        if (iconList?.isNotEmpty() == true) {
            val iconModel = iconList.first()
            icon = when (iconModel.name) {
                context.getString(keyless.data.utils.android.R.string.text_villa) -> R.drawable.marker_villa

                context.getString(keyless.data.utils.android.R.string.text_hotel) -> R.drawable.marker_hotel

                context.getString(keyless.data.utils.android.R.string.text_office) -> R.drawable.marker_office

                context.getString(keyless.data.utils.android.R.string.text_apartment) -> R.drawable.marker_apartment

                else -> R.drawable.marker_other
            }
        }

        markerOptions.icon(bitmapDescriptorFromVector(context, icon))
        return markerOptions
    }

    private fun bitmapDescriptorFromVector(
        context: Context,
        vectorResId: Int
    ): BitmapDescriptor {
        val vectorDrawable = ContextCompat.getDrawable(context, vectorResId)
        vectorDrawable!!.setBounds(
            0,
            0,
            vectorDrawable.intrinsicWidth,
            vectorDrawable.intrinsicHeight
        )
        val bitmap = Bitmap.createBitmap(
            vectorDrawable.intrinsicWidth,
            vectorDrawable.intrinsicHeight,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        vectorDrawable.draw(canvas)
        return BitmapDescriptorFactory.fromBitmap(bitmap)
    }
}