plugins {
    id("keyless.android.library")
    id("config.values")
    id("org.jetbrains.kotlin.kapt")
}

android {
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

dependencies {
    implementation(project(":core-common"))
    api(project(":data-common"))
    implementation(project(":data-network-android"))
    implementation(project(":core-lock-iseo"))
    implementation(keyless.google.play.services.location)
    implementation(keyless.google.play.services.maps)
    api(keyless.gson)
    implementation(keyless.okhttp.logging.interceptor)
    implementation(keyless.retrofit)
    implementation(keyless.retrofit.gson)

    api("com.hbb20:ccp:2.5.1")

    api("androidx.room:room-runtime:2.5.2")
    api("androidx.room:room-ktx:2.5.2")
    kapt("androidx.room:room-compiler:2.5.2")
    androidTestImplementation("androidx.room:room-testing:2.5.2")

    implementation(platform("com.google.firebase:firebase-bom:28.4.2"))
    implementation ("com.google.firebase:firebase-crashlytics-ktx")

    implementation("com.github.vedraj360:DesignerToast:0.1.3")
    api("com.github.rajputkapil:textviewdotsanimation:v1.0")
    implementation ("com.github.khoyron:Actionsheet-android:4")
    api("com.wdullaer:materialdatetimepicker:4.2.3")
}

configValues {
    val baseUrl = getLocalProperty("base.url") as String? ?: throw GradleException(
        "Missing base.url property in local.properties"
    )
    val baseImageUrl = getLocalProperty("base.image.url") as String? ?: throw GradleException(
        "Missing base.image.url property in local.properties"
    )
    val forgotPasswordUrl = getLocalProperty("forgot.password.url") as String? ?: throw GradleException(
        "Missing forgot.password.url property in local.properties"
    )

    setValues(
        "baseUrl" to baseUrl,
        "baseImageUrl" to baseImageUrl,
        "forgotPasswordUrl" to forgotPasswordUrl
    )
}