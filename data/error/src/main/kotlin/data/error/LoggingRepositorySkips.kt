package data.error

object LoggingRepositorySkips {

    fun skipConnectionIssue(ex: Exception): Boolean {
        val messagesToSkip = listOf(
            "No address associated with hostname",
            "Socket timeout has expired",
            "Read timed out",
            "Software caused connection abort",
            "Unable to resolve host",
            "unexpected end of stream",
            "Read error",
            "Failed to connect to",
            "failed to connect to",
            "SSL handshake aborted",
            "Connection reset",
            "Connect timeout has expired",
            "Network is unreachable",
            "connection closed",
            "Connection closed by peer",
            "isConnected failed: ETIMEDOUT",
            "SocketTimeoutException",
            "Socket closed",
            "Connection refused",
            "failed to connect to"
        )
        val stackTrace = ex.stackTraceToString()

        return messagesToSkip.any { stackTrace.contains(it) }
    }

    fun skipCoroutinesCancel(ex: Exception): Boolean {
        val messagesToSkip = listOf(
            "JobCancellationException",
            "Job was cancelled"
        )
        val stackTrace = ex.stackTraceToString()

        return messagesToSkip.any { stackTrace.contains(it) }
    }
}