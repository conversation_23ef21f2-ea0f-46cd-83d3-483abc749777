package data.network.android.models

import androidx.annotation.Keep

@Keep
class ModelCheckInPm {
    var success: Boolean = false
    var message: String = ""
    var assignments: ArrayList<ModelAssignCheckIns> = ArrayList()
}

@Keep
class ModelAssignCheckIns {
    var valid_from: String = ""
    var valid_to: String = ""
    var booking_number: String = ""
    var first_name: String = ""
    var last_name: String = ""
    var lock_name: String = ""
    var fullName: String = ""
}