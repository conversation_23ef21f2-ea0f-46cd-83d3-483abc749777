package data.network.android.models

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep
import data.network.android.LocksListResponse

@Keep
class ModelInstallerMaintenance {

    var success: Boolean = false
    var data: ArrayList<ModelData> = ArrayList()
    var total: Int = 0
}

@Keep
class ModelData() : Parcelable {
    var _id: String = ""
    var lock_id: String = ""
    var assignedUserId: String = ""
    var created_at: String = ""
    var updated_at: String = ""
    var assignedByUserId: String = ""
    var lock_id_string: String = ""
    var property_id_object: String = ""
    var status: Int = 0
    var __v: Int = 0
    var lock_info: ArrayList<LockInfoModel> = ArrayList()
    var maplockproperty: ModelMapLockproperty = ModelMapLockproperty()
    var property_details: LocksListResponse.PropertyDetailsModel = LocksListResponse.PropertyDetailsModel()
    var allotment: ModelAllotment = ModelAllotment()
    var company: ArrayList<CompanyInstallerModel> = ArrayList()

    constructor(parcel: Parcel) : this() {
        _id = parcel.readString()!!
        lock_id = parcel.readString()!!
        assignedUserId = parcel.readString()!!
        created_at = parcel.readString()!!
        updated_at = parcel.readString()!!
        assignedByUserId = parcel.readString()!!
        lock_id_string = parcel.readString()!!
        property_id_object = parcel.readString()!!
        status = parcel.readInt()
        __v = parcel.readInt()
        lock_info = parcel.createTypedArrayList(LockInfoModel)!!
        maplockproperty = parcel.readParcelable(ModelMapLockproperty::class.java.classLoader)!!
        property_details =
            parcel.readParcelable(LocksListResponse.PropertyDetailsModel::class.java.classLoader)!!
        allotment = parcel.readParcelable(ModelAllotment::class.java.classLoader)!!
        company = parcel.createTypedArrayList(CompanyInstallerModel)!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(_id)
        parcel.writeString(lock_id)
        parcel.writeString(assignedUserId)
        parcel.writeString(created_at)
        parcel.writeString(updated_at)
        parcel.writeString(assignedByUserId)
        parcel.writeString(lock_id_string)
        parcel.writeString(property_id_object)
        parcel.writeInt(status)
        parcel.writeInt(__v)
        parcel.writeTypedList(lock_info)
        parcel.writeParcelable(maplockproperty, flags)
        parcel.writeParcelable(property_details, flags)
        parcel.writeParcelable(allotment, flags)
        parcel.writeTypedList(company)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ModelData> {
        override fun createFromParcel(parcel: Parcel): ModelData {
            return ModelData(parcel)
        }

        override fun newArray(size: Int): Array<ModelData?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class CompanyInstallerModel() : Parcelable {
    var _id: String = ""
    var status: String = ""
    var company_name: String = ""
    var url: String = ""
    var address: String = ""
    var city: String = ""
    var zip_code: String = ""
    var country: String = ""
    var user_id: String = ""
    var total_licenses: String = ""
    var plan_id: String = ""
    var card_details: String = ""
    var created_at: String = ""
    var trade_license_number: String = ""
    var trn_number: String = ""
    var next_payment: String = ""
    var updated_at: String = ""

    constructor(parcel: Parcel) : this() {
        _id = parcel.readString()!!
        status = parcel.readString()!!
        company_name = parcel.readString()!!
        url = parcel.readString()!!
        address = parcel.readString()!!
        city = parcel.readString()!!
        zip_code = parcel.readString()!!
        country = parcel.readString()!!
        user_id = parcel.readString()!!
        total_licenses = parcel.readString()!!
        plan_id = parcel.readString()!!
        card_details = parcel.readString()!!
        created_at = parcel.readString()!!
        trade_license_number = parcel.readString()!!
        trn_number = parcel.readString()!!
        next_payment = parcel.readString()!!
        updated_at = parcel.readString()!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(_id)
        parcel.writeString(status)
        parcel.writeString(company_name)
        parcel.writeString(url)
        parcel.writeString(address)
        parcel.writeString(city)
        parcel.writeString(zip_code)
        parcel.writeString(country)
        parcel.writeString(user_id)
        parcel.writeString(total_licenses)
        parcel.writeString(plan_id)
        parcel.writeString(card_details)
        parcel.writeString(created_at)
        parcel.writeString(trade_license_number)
        parcel.writeString(trn_number)
        parcel.writeString(next_payment)
        parcel.writeString(updated_at)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<CompanyInstallerModel> {
        override fun createFromParcel(parcel: Parcel): CompanyInstallerModel {
            return CompanyInstallerModel(parcel)
        }

        override fun newArray(size: Int): Array<CompanyInstallerModel?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class LockInfoModel() : Parcelable {

    var _id: String = ""
    var alloted: Boolean = false
    var status: Int = 0
    var battery_level: Int = 254
    var inventory_status: Int = 254
    var name: String = ""
    var unique_key: String = ""
    var access_key: String = ""
    var encrypted_key: String = ""
    var provider: String = ""
    var lock_uid: String = ""
    var internal_id: String = ""
    var size: String = ""
    var colour: String = ""
    var order_id: String = ""
    var icon_id: String = ""
    var createdAt: String = ""
    var comment: String = ""

    constructor(parcel: Parcel) : this() {
        _id = parcel.readString()!!
        alloted = parcel.readByte() != 0.toByte()
        status = parcel.readInt()
        battery_level = parcel.readInt()
        inventory_status = parcel.readInt()
        name = parcel.readString()!!
        unique_key = parcel.readString()!!
        access_key = parcel.readString()!!
        encrypted_key = parcel.readString()!!
        provider = parcel.readString()!!
        lock_uid = parcel.readString()!!
        internal_id = parcel.readString()!!
        size = parcel.readString()!!
        colour = parcel.readString()!!
        order_id = parcel.readString()!!
        icon_id = parcel.readString()!!
        createdAt = parcel.readString()!!
        comment = parcel.readString()!!
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(_id)
        parcel.writeByte(if (alloted) 1 else 0)
        parcel.writeInt(status)
        parcel.writeInt(battery_level)
        parcel.writeInt(inventory_status)
        parcel.writeString(name)
        parcel.writeString(unique_key)
        parcel.writeString(access_key)
        parcel.writeString(encrypted_key)
        parcel.writeString(provider)
        parcel.writeString(lock_uid)
        parcel.writeString(internal_id)
        parcel.writeString(size)
        parcel.writeString(colour)
        parcel.writeString(order_id)
        parcel.writeString(icon_id)
        parcel.writeString(createdAt)
        parcel.writeString(comment)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<LockInfoModel> {
        override fun createFromParcel(parcel: Parcel): LockInfoModel {
            return LockInfoModel(parcel)
        }

        override fun newArray(size: Int): Array<LockInfoModel?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class ModelAllotment() : Parcelable {

    var _id: String = ""
    var lock_type: String = ""
    var lock_id: String = ""
    var alloted_by: String = ""
    var manager_type: String = ""
    var manager_id: String = ""
    var property_id: String = ""
    var created_at: String = ""
    var status: Int = 0
    var __v: Int = 0

    constructor(parcel: Parcel) : this() {
        _id = parcel.readString()!!
        lock_type = parcel.readString()!!
        lock_id = parcel.readString()!!
        alloted_by = parcel.readString()!!
        manager_type = parcel.readString()!!
        manager_id = parcel.readString()!!
        property_id = parcel.readString()!!
        created_at = parcel.readString()!!
        status = parcel.readInt()
        __v = parcel.readInt()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(_id)
        parcel.writeString(lock_type)
        parcel.writeString(lock_id)
        parcel.writeString(alloted_by)
        parcel.writeString(manager_type)
        parcel.writeString(manager_id)
        parcel.writeString(property_id)
        parcel.writeString(created_at)
        parcel.writeInt(status)
        parcel.writeInt(__v)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ModelAllotment> {
        override fun createFromParcel(parcel: Parcel): ModelAllotment {
            return ModelAllotment(parcel)
        }

        override fun newArray(size: Int): Array<ModelAllotment?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class ModelMapLockproperty() : Parcelable {
    var _id: String = ""
    var property_id: String = ""
    var lock_id: String = ""
    var floor_number: String = ""
    var appartment_number: String = ""
    var room_number: String = ""
    var created_at: String = ""
    var __v: Int = 0

    constructor(parcel: Parcel) : this() {
        _id = parcel.readString()!!
        property_id = parcel.readString()!!
        lock_id = parcel.readString()!!
        floor_number = parcel.readString()!!
        appartment_number = parcel.readString()!!
        room_number = parcel.readString()!!
        created_at = parcel.readString()!!
        __v = parcel.readInt()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(_id)
        parcel.writeString(property_id)
        parcel.writeString(lock_id)
        parcel.writeString(floor_number)
        parcel.writeString(appartment_number)
        parcel.writeString(room_number)
        parcel.writeString(created_at)
        parcel.writeInt(__v)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ModelMapLockproperty> {
        override fun createFromParcel(parcel: Parcel): ModelMapLockproperty {
            return ModelMapLockproperty(parcel)
        }

        override fun newArray(size: Int): Array<ModelMapLockproperty?> {
            return arrayOfNulls(size)
        }
    }
}