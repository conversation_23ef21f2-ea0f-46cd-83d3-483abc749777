package data.network.android.models

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

@Keep
data class MoreInfoModel(val name: String, val id: Int)

@Keep
data class LockHistoryModel(
    var status: String = "1",
    var device_model: String= "",
    var lockId: String = "",
    var mobile_id: String = "",
    var created_at: String = "",
    var company_id: String = ""
): Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readString()!!
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(status)
        parcel.writeString(device_model)
        parcel.writeString(lockId)
        parcel.writeString(mobile_id)
        parcel.writeString(created_at)
        parcel.writeString(company_id)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<LockHistoryModel> {
        override fun createFromParcel(parcel: Parcel): LockHistoryModel {
            return LockHistoryModel(parcel)
        }

        override fun newArray(size: Int): Array<LockHistoryModel?> {
            return arrayOfNulls(size)
        }
    }
}