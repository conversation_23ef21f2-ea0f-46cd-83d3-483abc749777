package data.network.android.models

import androidx.annotation.Keep

@Keep
data class UserProfileReponse(
    var success: Boolean?,
    var message: String?,
    var profile_photo: String?,
    var user: User?
) {
    @Keep
    data class User(
        var contact_person_name: String?,
        var country_code: String?,
        var email: String?,
        var mobile_number: Long?,
        var name: String?,
        var profile_photo: String?,
        var first_name: String?,
        var last_name: String?,
        var username: String?
    )
}