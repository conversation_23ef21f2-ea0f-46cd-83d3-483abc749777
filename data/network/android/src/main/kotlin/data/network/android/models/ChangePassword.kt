package data.network.android.models

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
object ChangePassword {

    @Keep
    data class Req(
        val user_id: String,
        val password: String,
        @SerializedName("confirm_password")
        val confirm_password: String,
        val old_password: String
    )

    @Keep
    data class Res(
        val success: Boolean?,
        val message: String,
        val id: String? = null
    )
}