package data.network.android.models

import androidx.annotation.Keep

@Keep
object ForgotPassword {
    data class Req(
        val email: String
    )

    @Keep
    data class Res(
        val success: Boolean?,
        val id: String?,
        val message: String?
//    {"success":false,"message":"No user found with this email."}
        // {"success":true,"id":"6328aa2d45d9bc233f3c243f","message":"OTP sent <NAME_EMAIL>"}
    )
}