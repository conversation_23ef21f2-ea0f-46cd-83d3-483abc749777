package data.network.android

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep
import core.common.encryption.Encryption
import data.common.preferences.Preferences

@Keep
class GetInternalLockId {
    var success: Boolean = false
    var message: String = ""
    var lockId = ""
}

@Keep
class GetAccessKeyModel() : Parcelable {
    var success: Boolean = false
    var message: String = ""
    var data = DataKeyModel()

    constructor(parcel: Parcel) : this() {
        success = parcel.readByte() != 0.toByte()
        message = parcel.readString()!!
        data = parcel.readParcelable(DataKeyModel::class.java.classLoader)!!
    }

    @Keep
    class DataKeyModel() : Parcelable {

        var _id: String = ""
        var alloted: String = ""
        var status: Int = 0
        var name: String = ""
        var image: String = ""
        var unique_key: String = ""
        var access_key: String = ""
        var encrypted_key: String = ""
        var provider: String = ""
        var lock_uid: String = ""
        var battery_level: String = ""
        var desc: String = ""
        var time_zone: String = ""
        var inventory_status: String = ""
        var internal_id: String = ""
        var size: String = ""
        var colour: String = ""
        var order_id: String = ""
        var createdAt: String = ""
        var messer_token: String = ""
        var __v: Int = 0
        var installationId: String = ""

        constructor(parcel: Parcel) : this() {
            _id = parcel.readString()!!
            alloted = parcel.readString()!!
            status = parcel.readInt()
            name = parcel.readString()!!
            image = parcel.readString()!!
            unique_key = parcel.readString()!!
            access_key = parcel.readString()!!
            encrypted_key = parcel.readString()!!
            provider = parcel.readString()!!
            lock_uid = parcel.readString()!!
            battery_level = parcel.readString()!!
            desc = parcel.readString()!!
            time_zone = parcel.readString()!!
            inventory_status = parcel.readString()!!
            internal_id = parcel.readString()!!
            size = parcel.readString()!!
            colour = parcel.readString()!!
            order_id = parcel.readString()!!
            createdAt = parcel.readString()!!
            messer_token = parcel.readString()!!
            __v = parcel.readInt()
            installationId = parcel.readString()!!
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeString(_id)
            parcel.writeString(alloted)
            parcel.writeInt(status)
            parcel.writeString(name)
            parcel.writeString(image)
            parcel.writeString(unique_key)
            parcel.writeString(access_key)
            parcel.writeString(encrypted_key)
            parcel.writeString(provider)
            parcel.writeString(lock_uid)
            parcel.writeString(battery_level)
            parcel.writeString(desc)
            parcel.writeString(time_zone)
            parcel.writeString(inventory_status)
            parcel.writeString(internal_id)
            parcel.writeString(size)
            parcel.writeString(colour)
            parcel.writeString(order_id)
            parcel.writeString(createdAt)
            parcel.writeString(messer_token)
            parcel.writeInt(__v)
            parcel.writeString(installationId)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<DataKeyModel> {
            override fun createFromParcel(parcel: Parcel): DataKeyModel {
                return DataKeyModel(parcel)
            }

            override fun newArray(size: Int): Array<DataKeyModel?> {
                return arrayOfNulls(size)
            }
        }

        fun unlockAccessKey(): String {
            return when {
                provider.contentEquals("Messerschmitt", true) -> {
                    Encryption.decrypt(encrypted_key, Preferences.uuid.get()) ?: encrypted_key
                }
                else -> access_key
            }
        }
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeByte(if (success) 1 else 0)
        parcel.writeString(message)
        parcel.writeParcelable(data, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<GetAccessKeyModel> {
        override fun createFromParcel(parcel: Parcel): GetAccessKeyModel {
            return GetAccessKeyModel(parcel)
        }

        override fun newArray(size: Int): Array<GetAccessKeyModel?> {
            return arrayOfNulls(size)
        }
    }
}