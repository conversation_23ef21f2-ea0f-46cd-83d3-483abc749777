package data.network.android

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

@Keep
class GetRoutineResponse() : Parcelable {
    var routines: ArrayList<RoutineModel> = ArrayList()
    var success: Boolean = false
    var total_count: Int = 0

    constructor(parcel: Parcel) : this() {
        routines = parcel.createTypedArrayList(RoutineModel)!!
        success = parcel.readByte() != 0.toByte()
        total_count= parcel.readInt()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeTypedList(routines)
        parcel.writeByte(if (success) 1 else 0)
        parcel.writeInt(total_count)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<GetRoutineResponse> {
        override fun createFromParcel(parcel: Parcel): GetRoutineResponse {
            return GetRoutineResponse(parcel)
        }

        override fun newArray(size: Int): Array<GetRoutineResponse?> {
            return arrayOfNulls(size)
        }
    }
}

@Keep
class RoutineModel() : Parcelable {
    var __v: Int = 0
    var _id: String = ""
    var always_open: Boolean = false
    var created_at: String = ""
    var created_by: String = ""
    var iseo_id: Int = 0
    var totalLocks: Int = 0
    var totalPersons: Int = 0
    var name: String = ""
    var status: Boolean = false

    constructor(parcel: Parcel) : this() {
        __v = parcel.readInt()
        _id = parcel.readString()!!
        always_open = parcel.readByte() != 0.toByte()
        created_at = parcel.readString()!!
        created_by = parcel.readString()!!
        iseo_id = parcel.readInt()
        totalLocks = parcel.readInt()
        totalPersons = parcel.readInt()
        name = parcel.readString()!!
        status = parcel.readByte() != 0.toByte()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(__v)
        parcel.writeString(_id)
        parcel.writeByte(if (always_open) 1 else 0)
        parcel.writeString(created_at)
        parcel.writeString(created_by)
        parcel.writeInt(iseo_id)
        parcel.writeInt(totalLocks)
        parcel.writeInt(totalPersons)
        parcel.writeString(name)
        parcel.writeByte(if (status) 1 else 0)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<RoutineModel> {
        override fun createFromParcel(parcel: Parcel): RoutineModel {
            return RoutineModel(parcel)
        }

        override fun newArray(size: Int): Array<RoutineModel?> {
            return arrayOfNulls(size)
        }
    }
}