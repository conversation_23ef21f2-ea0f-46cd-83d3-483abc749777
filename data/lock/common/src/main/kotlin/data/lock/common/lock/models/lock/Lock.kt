package data.lock.common.lock.models.lock

import core.common.encryption.Encryption
import data.common.preferences.Preferences

data class Lock(
    val id: String,
    val assignment: LockAssignment,
    val ownerId: String,
    val unitId: String,
    val bookingNumber: String,
    val privacy: Boolean,
    val privacyChanged: <PERSON>olean,
    val companyCheckIn: <PERSON>olean,
    val checkIn: Boolean,
    val totalCheckins: Int,
    val accessKey: String,
    val batteryLevel: Int,
    val createdAt: String,
    val image: String,
    val lockUid: String,
    val name: String,
    val desc: String,
    val provider: String,
    val status: Int,
    val uniqueKey: String,
    val timeZone: String,
    val internalId: String,
    val encryptedKey: String,
    val privacyMode: Boolean,
    val primary: Boolean,
    val privacyPermission: Boolean,
    val privacyOwner: Boolean,
    val icons: List<Icon>,
    val property: PropertyDetails,
    val firmwareUpdated: Boolean,
    val firmwareVersion: String,
    val firmwareAvailableVersion: String,
    val passcodeId: String,
    val passcode: String,
    val tedeeLockId: String
) {
    fun unlockAccessKey(): String {
        return when {
            provider.contentEquals("Messerschmitt", true) -> {
                Encryption.decrypt(encryptedKey, Preferences.uuid.get()) ?: encryptedKey
            }
            else -> accessKey
        }
    }
}