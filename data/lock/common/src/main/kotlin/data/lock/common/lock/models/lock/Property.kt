package data.lock.common.lock.models.lock

data class Property(
    val latitude: String,
    val longitude: String,
    val managerId: String,
    val managerType: String,
    val emirate: String,
    val area: String,
    val buildingName: String,
    val totalFloors: Long,
    val createdAt: String,
    val iconId: String,
    val supportCallNumber: String,
    val supportWhatsappNumber: String,
    val icons: List<Icon>,
    val count: Int,
    val id: String,
    val laundryNumber: String,
    val groceryNumber: String,
    val maintenanceNumber: String
)

data class PropertyDetails(
    val latitude: String,
    val longitude: String,
    val managerId: String,
    val managerType: String,
    val emirate: String,
    val area: String,
    val buildingName: String,
    val totalFloors: Long,
    val createdAt: String,
    val iconId: String,
    val supportCallNumber: String,
    val supportWhatsappNumber: String,
    val count: Int,
    val id: String,
    val floor: String,
    val name: String,
    val roomNumber: String,
    val laundryNumber: String,
    val groceryNumber: String,
    val maintenanceNumber: String,
    val apartmentNumber: String,
    val mapId: String
)