package data.lock.common.lock.models.dto

import data.lock.common.lock.models.PlatformDetails
import kotlinx.serialization.Serializable

@Serializable
internal data class PlatformDetailsDto(
    val success: Boolean?,
    val message: String?,
    val iseo_url: String?,
    val plant_name: String?
) {
    fun toModel(): PlatformDetails {
        return PlatformDetails(
            iseoPlatform = iseo_url!!,
            plantName = plant_name!!
        )
    }
}