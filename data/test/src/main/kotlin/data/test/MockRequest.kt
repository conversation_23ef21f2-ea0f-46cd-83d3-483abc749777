package data.test

import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.http.client.HttpResponse
import core.http.client.test.models.TestRequestResponse
import kotlinx.serialization.json.JsonElement

fun mockRequest(
    url: String,
    requestBody: JsonElement? = null,
    responseBody: String,
    method: HttpRequest.Method = HttpRequest.Method.GET,
    responseCode: Int = 200,
    responseHeaders: Map<String, List<String>> = emptyMap(),
    requestHeaders: List<HttpHeader> = emptyList()
): TestRequestResponse {
    val request = HttpRequest(
        url = url,
        method = method,
        headers = requestHeaders,
        body = requestBody
    )
    val sampleResponse = HttpResponse(
        request = request,
        responseCode = responseCode,
        responseHeaders = responseHeaders,
        body = responseBody
    )

    return TestRequestResponse(
        request = request,
        response = sampleResponse
    )
}