package data.test

import app.cash.sqldelight.db.SqlDriver
import app.cash.sqldelight.driver.jdbc.sqlite.JdbcSqliteDriver
import core.http.client.test.MockHttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.test.CLILogger

val testHostname = "https://api.example.com"
val authToken = "Bearer dummy.token.sample"

fun testDriver(): SqlDriver = JdbcSqliteDriver(JdbcSqliteDriver.IN_MEMORY)
fun testLogger(): Logger = CLILogger("")
fun testHttpClient(): MockHttpClient = MockHttpClient(testLogger())