package data.company.models

data class CompanyProfile(
    val id: String,
    val status: Long?,
    val timezoneOffset: String,
    val timezoneName: String,
    val companyName: String?,
    val businessLia: String,
    val url: String?,
    val address: String,
    val city: String,
    val userId: String?,
    val totalLicenses: Long?,
    val cardDetails: String?,
    val createdAt: String?,
    val version: Long?,
    val updatedAt: String?,
    val planId: String?,
    val trnNumber: String,
    val businessType: String,
    val tradeLicenseNumber: String?,
    val country: String?,
    val zipCode: String?,
    val checkIn: Boolean
)