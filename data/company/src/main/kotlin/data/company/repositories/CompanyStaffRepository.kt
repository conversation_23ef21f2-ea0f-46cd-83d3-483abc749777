package data.company.repositories

import data.company.models.StaffMember
import data.company.models.StaffMemberRole

interface CompanyStaffRepository {
    suspend fun getAllStaff(): List<StaffMember>

    fun getById(id: String): StaffMember

    suspend fun getRoles(): List<StaffMemberRole>

    suspend fun addStaff(
        countryCode: String,
        mobileNumber: String,
        email: String,
        username: String,
        firstName: String,
        lastName: String,
        role: String,
        passportNumber: String
    ): String

    suspend fun updateStaff(
        staffId: String,
        countryCode: String,
        mobileNumber: String,
        email: String,
        username: String,
        firstName: String,
        lastName: String,
        role: String,
        passportNumber: String
    ): String
}