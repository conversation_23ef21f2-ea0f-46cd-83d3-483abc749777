package domain.regula.repositories

import core.common.status.StatusRepository
import domain.regula.models.ScreenState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update

class ScreenStateRepository(
    private val status: StatusRepository
) {
    private val state = MutableStateFlow(emptyState())
    var current: ScreenState = emptyState()
        private set

    val stream = combination(
        screenState = state,
        status = status.stream
    ) { screen, status ->
        screen.copy(
            status = status
        )
    }.onEach { current = it }

    fun update(block: (ScreenState) -> ScreenState) {
        state.update { block(it) }
    }
}