package domain.regula.models

import core.common.serialization.json
import core.common.status.Status
import core.regula.documents.DocumentScanResult
import core.regula.documents.FaceLiveNessResult
import core.regula.documents.MatchFacesResult
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.addJsonObject
import kotlinx.serialization.json.buildJsonArray
import kotlinx.serialization.json.put
import kotlinx.serialization.json.putJsonObject

data class ScreenState(
    val bookingNumber: String,

    val state: DocumentTypeState,
    val frontSide: DocumentScanResult?,
    val backSide: DocumentScanResult?,

    val faceVerificationImage: FaceLiveNessResult?,
    val similarityResults: MatchFacesResult?,

    val status: List<Status>
) {

    val extra = json.encodeToString(
        buildJsonArray {
            addJsonObject {
                putJsonObject("front") {
                    frontSide?.textResults?.forEach {
                        put(it.key, it.value)
                    }
                }
                putJsonObject("back") {
                    backSide?.textResults?.forEach {
                        put(it.key, it.value)
                    }
                }
            }
        }
    )

    enum class DocumentTypeState {
        NONE,
        UAE_ID,
        PASSPORT
    }
}