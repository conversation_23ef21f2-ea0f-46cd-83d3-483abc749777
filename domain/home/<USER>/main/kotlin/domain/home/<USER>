package domain.home

import domain.home.models.Event
import domain.home.models.ScreenEvent
import domain.home.usecases.HomeUseCases

class ViewModel(
    private val useCases: HomeUseCases
) {

    suspend fun onEvent(event: Event) = when (event) {
        is ScreenEvent -> onScreenEvent(event)
    }

    private suspend fun onScreenEvent(event: ScreenEvent) = when (event) {
        is ScreenEvent.Refresh -> {
            event.callback(useCases.screen.fetch.execute(token = event.token, uid = event.uid, isAdmin = event.isAdmin))
        }
    }
}