package domain.settings.support.models

sealed interface Event

sealed interface ScreenEvent : Event {
    object InitNearBy : ScreenEvent

    object ResumeNearBy : ScreenEvent

    object PauseNearBy : ScreenEvent

    object EndNearBy : ScreenEvent
}

sealed interface DiagnosticEvent : Event {
    class SendLogs(
        val internalId: String,
        val authentication: String
    ) : DiagnosticEvent
}