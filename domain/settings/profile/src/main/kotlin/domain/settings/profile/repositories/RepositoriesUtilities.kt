package domain.settings.profile.repositories

import core.common.status.Status
import domain.settings.profile.models.ScreenState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine

internal inline fun ScreenStateRepository.combination(
    screenState: Flow<ScreenState>,
    status: Flow<List<Status>>,
    crossinline transform: suspend (
        screenState: ScreenState,
        status: List<Status>
    ) -> ScreenState
): Flow<ScreenState> {
    return combine(
        screenState,
        status
    ) { args: Array<*> ->
        transform(
            args[0] as ScreenState,
            args[1] as List<Status>
        )
    }
}

internal fun emptyState(): ScreenState {
    return ScreenState(isLoading = false)
}