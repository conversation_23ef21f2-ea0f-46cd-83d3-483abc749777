package domain.settings.profile

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.user.account.repositories.UserAccountRepository
import domain.settings.profile.models.EmailEvent
import domain.settings.profile.models.Event
import domain.settings.profile.models.ProfileEvent
import domain.settings.profile.models.ScreenEvent
import domain.settings.profile.repositories.ScreenStateRepository
import domain.settings.profile.repositories.SideEffectsRepository
import domain.settings.profile.usecases.ProfileUseCases

class ViewModel(
    logger: Logger,
    userAccount: UserAccountRepository,
    screenState: ScreenStateRepository,
    status: StatusRepository,
    sideEffects: SideEffectsRepository
) {

    val screenStream = screenState.stream
    val sideEffectsStream = sideEffects.stream

    private val useCases = ProfileUseCases(
        logger = logger,
        userAccount = userAccount,
        screen = screenState,
        status = status,
        sideEffects = sideEffects
    )

    suspend fun onEvent(event: Event) = when (event) {
        is EmailEvent -> onEmailEvent(event)
        is ScreenEvent -> onScreenEvent(event)
        is ProfileEvent -> onProfileEvent(event)
    }

    private suspend fun onEmailEvent(event: EmailEvent) = when (event) {
        is EmailEvent.SendOtp -> useCases.sendEmailOtp.execute(event)
        is EmailEvent.VerifyEmail -> useCases.verifyEmail.execute(event)
    }

    private suspend fun onScreenEvent(event: ScreenEvent) = when (event) {
        is ScreenEvent.UpdateError -> useCases.updateError.execute(event)
    }

    private suspend fun onProfileEvent(event: ProfileEvent) = when (event) {
        is ProfileEvent.ChangePassword -> useCases.changePassword.execute(event)

        is ProfileEvent.UpdateProfile -> useCases.updateProfile.execute(event)

        is ProfileEvent.GetUserProfile -> useCases.getUserProfile.execute(event)

        is ProfileEvent.DeleteAccount -> useCases.deleteAccount.execute()
    }
}