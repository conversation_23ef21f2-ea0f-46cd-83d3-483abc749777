package domain.settings.profile.models

import data.user.account.models.SendOtpEmailResponse
import data.user.account.models.UserProfileResponse
import data.user.account.models.VerifyEmailResponse

sealed interface SideEffect {

    class OnSendOtp(val response: SendOtpEmailResponse) : SideEffect

    class OnVerifyEmail(val response: VerifyEmailResponse) : SideEffect

    class ErrorToast(val exception: Exception) : SideEffect

    class ChangePasswordMessage(val message: String) : SideEffect

    class UpdateProfileMessage(val message: String) : SideEffect

    class UserProfile(val response: UserProfileResponse) : SideEffect

    object AccountDeleted : SideEffect
}