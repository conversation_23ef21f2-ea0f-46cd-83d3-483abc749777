package domain.settings.profile.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.user.account.repositories.UserAccountRepository
import domain.settings.profile.repositories.ScreenStateRepository
import domain.settings.profile.repositories.SideEffectsRepository

internal class ProfileUseCases(
    screen: ScreenStateRepository,
    logger: Logger,
    userAccount: UserAccountRepository,
    sideEffects: SideEffectsRepository,
    status: StatusRepository
) {

    val sendEmailOtp = SendEmailOtpUseCase(
        logger = logger,
        userAccount = userAccount,
        sideEffects = sideEffects,
        status = status
    )
    val verifyEmail = VerifyEmailUseCase(
        logger = logger,
        userAccount = userAccount,
        sideEffects = sideEffects,
        status = status
    )
    val updateError = UpdateErrorUseCase(screen, logger)
    val changePassword = ChangePasswordUseCase(userAccount, sideEffects, logger, status)
    val updateProfile = UpdateProfileUseCase(userAccount, sideEffects, logger, status)
    val getUserProfile = GetUserProfileUseCase(userAccount, sideEffects, logger, status)
    val deleteAccount = DeleteAccountUseCase(userAccount, sideEffects, logger, status)
}