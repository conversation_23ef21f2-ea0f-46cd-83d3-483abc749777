package domain.settings.company.staff.add

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.company.repositories.CompanyStaffRepository
import domain.settings.company.staff.add.models.Event
import domain.settings.company.staff.add.models.ScreenEvent
import domain.settings.company.staff.add.models.StaffEvent
import domain.settings.company.staff.add.repositories.ScreenStateRepository
import domain.settings.company.staff.add.repositories.SideEffectsRepository
import domain.settings.company.staff.add.usecases.AddStaffUseCases

class ViewModel(
    private val status: StatusRepository,
    private val staff: CompanyStaffRepository,
    logger: Logger
) {
    private val sideEffects = SideEffectsRepository()
    private val screenState = ScreenStateRepository(status = status)
    private val useCases = AddStaffUseCases(
        screen = screenState,
        sideEffects = sideEffects,
        logger = logger,
        status = status,
        staff = staff
    )
    val sideEffectsStream = sideEffects.stream
    val stateScreenStream = screenState.stream
    suspend fun onEvent(event: Event) = when (event) {
        is ScreenEvent -> onScreenEvent(event)

        is StaffEvent -> onStaffEvent(event)
    }

    private suspend fun onScreenEvent(event: ScreenEvent) = when (event) {
        is ScreenEvent.SelectRole -> {
            useCases.ui.selectRole(event)
        }

        is ScreenEvent.ClickToSelectRole -> {
            useCases.fetchRoles.execute(event)
        }

        is ScreenEvent.Init -> {
            useCases.initScreen.execute(event)
        }
    }

    private suspend fun onStaffEvent(event: StaffEvent) = when (event) {
        is StaffEvent.AddStaff -> {
            useCases.addStaff.execute(event)
        }

        is StaffEvent.EditStaff -> {
            useCases.editStaff.execute(event)
        }
    }
}