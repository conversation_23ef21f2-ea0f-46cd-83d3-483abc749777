package domain.settings.company.staff.add.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.company.repositories.CompanyStaffRepository
import domain.settings.company.staff.add.repositories.ScreenStateRepository
import domain.settings.company.staff.add.repositories.SideEffectsRepository

internal class AddStaffUseCases(
    private val screen: ScreenStateRepository,
    private val sideEffects: SideEffectsRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val staff: CompanyStaffRepository
) {
    val initScreen = InitScreenUseCase(
        screen = screen,
        logger = logger,
        status = status,
        staff = staff
    )

    val fetchRoles = FetchRolesUseCase(
        screen = screen,
        sideEffects = sideEffects,
        logger = logger,
        status = status,
        staff = staff
    )

    val addStaff = AddStaffUseCase(
        sideEffects = sideEffects,
        logger = logger,
        status = status,
        staff = staff
    )

    val editStaff = EditStaffUseCase(
        sideEffects = sideEffects,
        logger = logger,
        status = status,
        staff = staff
    )

    val ui = UIUseCases(
        screen = screen,
        sideEffects = sideEffects,
        logger = logger,
        status = status
    )
}