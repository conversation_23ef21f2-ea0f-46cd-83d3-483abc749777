package data.settings.company.repositories

import data.settings.company.models.ScreenState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update

class ScreenStateRepository {
    private val state = MutableStateFlow(emptyState())
    val stream = combination(
        screenState = state
    ) { screen ->
        screen.copy()
    }

    fun update(block: (ScreenState) -> ScreenState) {
        state.update { block(it) }
    }
}