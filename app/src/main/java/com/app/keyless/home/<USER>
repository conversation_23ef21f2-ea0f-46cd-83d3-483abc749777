package com.app.keyless.home

import android.content.ContentResolver
import android.content.Context
import android.graphics.SurfaceTexture
import android.media.MediaPlayer
import android.net.Uri
import android.view.Surface
import android.view.TextureView
import androidx.appcompat.app.AppCompatActivity
import com.app.keyless.R

abstract class SplashCompact : AppCompatActivity(), TextureView.SurfaceTextureListener {
    var mediaPlayer: MediaPlayer? = null

    private fun getUriFromRawFile(context: Context, rawResourceId: Int): Uri? {
        return Uri.Builder()
            .scheme(ContentResolver.SCHEME_ANDROID_RESOURCE)
            .authority(context.packageName)
            .path(rawResourceId.toString())
            .build()
    }

    override fun onSurfaceTextureDestroyed(p0: SurfaceTexture): Boolean {
        if (mediaPlayer != null) {
            mediaPlayer?.stop()
            mediaPlayer?.release()
            mediaPlayer = null
        }
        return true
    }

    override fun onSurfaceTextureUpdated(p0: SurfaceTexture) {
    }

    override fun onSurfaceTextureSizeChanged(p0: SurfaceTexture, p1: Int, p2: Int) {
    }

    override fun onSurfaceTextureAvailable(p0: SurfaceTexture, p1: Int, p2: Int) {
        val surface = Surface(p0)
        mediaPlayer = MediaPlayer()
//        mediaPlayer?.setAudioAttributes(
//            AudioAttributes.Builder()
//                .setFlags(AudioAttributes.FLAG_AUDIBILITY_ENFORCED)
//                .setLegacyStreamType(AudioManager.STREAM_MUSIC)
//                .setUsage(AudioAttributes.USAGE_ALARM)
//                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
//                .build())
        val fileName = "android.resource://$packageName/" + R.raw.new_vid
        mediaPlayer?.setDataSource(this, getUriFromRawFile(this, R.raw.new_vid)!!)
//        mediaPlayer?.setDataSource(getApplicationContext(), Uri.parse("android.resource://com.keyless_dubai/" + R.raw.splash_vid))
        mediaPlayer?.setSurface(surface)
//        mediaPlayer?.setVideoScalingMode(MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING);
    }

    fun loadVideo() {
        mediaPlayer?.prepare()
        mediaPlayer?.setOnCompletionListener {
            mediaPlayer = null
        }
        mediaPlayer?.setOnPreparedListener {
            mediaPlayer?.start()
            loadFragmentInBack(it)
        }
    }
    abstract fun loadFragmentInBack(mediaPlayer: MediaPlayer)
}