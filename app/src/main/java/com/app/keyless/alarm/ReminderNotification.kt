package com.app.keyless.alarm

import android.app.Activity
import android.app.AlarmManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.NOTIFICATION_SERVICE
import android.content.Intent
import android.os.Build
import com.app.keyless.home.DashboardActivity
import data.utils.android.CommonValues.Companion.NOTIFICATION24
import data.utils.android.settings.SharedPreferenceUtils
import java.util.*

class ReminderNotification : BroadcastReceiver() {
    private lateinit var mNotification: Notification

    companion object {
        const val QUESTION_ID = "questionid"
        const val NOTIFICATION_FROM = "notificaitonFrom"
        const val CHANNEL_ID = "alarm_notification"
        const val CHANNEL_NAME = "Notification"
    }

    override fun onReceive(context: Context, intent: Intent) {
        val dismiss = intent.extras?.getInt("dismiss", 0)
        val type = intent.extras?.getString("type")
        val timestamp = intent.extras?.getLong("timestamp", 0)

        if (dismiss != 0) {
            dismiss?.let {
                val manager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                manager.cancel(dismiss)
            }
        } else if (type == "8") {
            SharedPreferenceUtils.getInstance(context).show8HoursWarning = true
        } else {
            createChannel(context)
            showNotification(context, NOTIFICATION24(context))
        }
    }

    private fun createChannel(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Create the NotificationChannel, but only on API 26+ because
            // the NotificationChannel class is new and not in the support library

            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            val importance = NotificationManager.IMPORTANCE_HIGH
            val notificationChannel = NotificationChannel(CHANNEL_ID, CHANNEL_NAME, importance)
            notificationChannel.enableVibration(true)
            notificationChannel.setShowBadge(true)
            notificationChannel.enableLights(true)
//            notificationChannel.lightColor = Color.parseColor("#e8334a")
//            notificationChannel.description = getString(keyless.data.utils.android.R.string.facebook_app_id)
            notificationChannel.lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            notificationManager.createNotificationChannel(notificationChannel)
        }
    }

    private fun setRepeatingAlarm(
        qID: Int,
        timeInMilliSeconds: Long,
        context: Context,
        intent: Intent
    ) {
        val alarmManager = context.getSystemService(Activity.ALARM_SERVICE) as AlarmManager
        val startTime = Calendar.getInstance()
        startTime.timeInMillis = timeInMilliSeconds
        startTime.add(Calendar.DATE, 1)
        val time = startTime.timeInMillis
        intent.putExtra("timestamp", time)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            qID,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT
        )

//        val formattedTimeAmPm = BasicHelper.getFullDate(time)
//        Log.e("//", "set reperting alarm time :$formattedTimeAmPm ")
//        alarmManager.setAlarmClock(AlarmManager.AlarmClockInfo(time, pendingIntent), pendingIntent)
    }

    private fun showNotification(context: Context, intent: String) {
        val notificationManager: NotificationManager
        val notifyIntent = Intent(context, DashboardActivity::class.java)

        val title = "Reminder to update tasks done"
        notifyIntent.putExtra("title", title)
        notifyIntent.putExtra("message", title)
        notifyIntent.putExtra("notification", true)

        notifyIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            notifyIntent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )
//            val res = this.resources
//            val uri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mNotification = Notification.Builder(context, CHANNEL_ID)
                .setContentIntent(pendingIntent)
                .setSmallIcon(keyless.data.utils.android.R.mipmap.ic_launcher)
//                    .setColor(resources.getColor(R.color.colorPrimary))
                .setAutoCancel(true)
                .setContentTitle(title)
                .setStyle(
                    Notification.BigTextStyle()
                        .bigText(title)
                )
                .setContentText(title).build()
        } else {
            mNotification = Notification.Builder(context)
                // Set the intent that will fire when the user taps the notification
                .setContentIntent(pendingIntent)
                .setSmallIcon(keyless.data.utils.android.R.mipmap.ic_launcher)
//                    .setColor(resources.getColor(R.color.colorPrimary))
                //                    .setLargeIcon(BitmapFactory.decodeResource(res, R.mipmap.app_logo))
                .setAutoCancel(true)
                .setPriority(Notification.PRIORITY_MAX)
                .setContentTitle(title)
                .setStyle(
                    Notification.BigTextStyle()
                        .bigText(title)
                )
//                    .setSound(uri)
                .setContentText(title).build()
        }

        notificationManager = context.getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(1001, mNotification)
    }
}