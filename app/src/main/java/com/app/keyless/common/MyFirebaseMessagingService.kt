package com.app.keyless.common

import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import androidx.core.app.NotificationCompat
import com.app.keyless.R
import com.app.keyless.home.DashboardActivity
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import feature.dfu.view.nearbyDfuActivity.DownloadStatus
import org.greenrobot.eventbus.EventBus
import java.lang.ref.WeakReference

@SuppressLint("MissingFirebaseInstanceTokenRefresh")
class MyFirebaseMessagingService : FirebaseMessagingService() {

    private val contextRef: WeakReference<Context> = WeakReference(this)

    @SuppressLint("MissingPermission")
    override fun onMessageReceived(p0: RemoteMessage) {
        var intent: Intent? = null

        intent = Intent(contextRef.get(), DashboardActivity::class.java)
        intent.putExtra("isNotification", "1")

        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
//        intent.addFlags(Intent.FLAG_A)
        val pendingIntent = PendingIntent.getActivity(
            contextRef.get()!!,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val channelId = contextRef.get()!!.getString(
            keyless.data.utils.android.R.string.default_notification_channel_id
        )
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)

        val notificationBuilder = NotificationCompat.Builder(contextRef.get()!!, channelId)
            .setSmallIcon(keyless.data.utils.android.R.mipmap.ic_launcher)
            .setContentTitle(p0.notification!!.title)
            .setContentText(p0.notification!!.body)
            .setAutoCancel(true)
            .setBadgeIconType(NotificationCompat.BADGE_ICON_SMALL)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setSound(defaultSoundUri)
//            .setContentIntent(pendingIntent)

        val notificationManager =
            contextRef.get()!!.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Since android Oreo notification channel is needed.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                getString(R.string.app_name),
                NotificationManager.IMPORTANCE_HIGH
            )
            notificationManager.createNotificationChannel(channel)
        }
//        val extra = Intent()
//        extra.action = CommonValues.NOTIFICATION_UPDATE
//        sendBroadcast(extra)

        notificationManager.notify(0, notificationBuilder.build())

        EventBus.getDefault().post(DownloadStatus(true))
    }
}