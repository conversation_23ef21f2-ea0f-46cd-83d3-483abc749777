<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".home.moreSettings.about.AboutUsActivity">

    <ImageView
        android:id="@+id/backBtnAbout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="20dp"
        style="@style/ImageMirror"
        android:layout_marginTop="16dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/propertyTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/about_us"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtnAbout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtnAbout" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtnAbout"
        app:layout_constraintVertical_bias="0.0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_bold_700"
                android:text="@string/about_the_app"
                android:textColor="@color/black"
                android:layout_marginTop="16dp"
                android:textSize="16dp" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/about_body1"
                android:textColor="@color/grey"
                android:layout_marginTop="8dp"
                android:textSize="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_bold_700"
                android:text="@string/property_manager"
                android:textColor="@color/black"
                android:layout_marginTop="16dp"
                android:textSize="16dp" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/about_body2"
                android:textColor="@color/grey"
                android:layout_marginTop="8dp"
                android:textSize="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_bold_700"
                android:text="@string/guest"
                android:textColor="@color/black"
                android:layout_marginTop="16dp"
                android:textSize="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_regular_400"
                android:text="@string/about_body3"
                android:textColor="@color/grey"
                android:layout_marginTop="8dp"
                android:textSize="16dp"
                android:layout_marginBottom="50dp"/>


        </LinearLayout>


    </androidx.core.widget.NestedScrollView>



</androidx.constraintlayout.widget.ConstraintLayout>