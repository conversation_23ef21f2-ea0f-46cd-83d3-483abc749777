name: "Checkout project submodules"
description: "Checkout project submodules"
inputs:
  shell:
    required: true
    description: "Shell to use depending on runner os"
  submodule-token:
    required: true
    description: "Submodule repository secret access token"
runs:
  using: "composite"
  steps:
    - name: Fix git URLs
      run: echo -e '[url "https://github.com/"]\n  insteadOf = "**************:"' >> ~/.gitconfig
      shell: ${{ inputs.shell }}

    - name: Checkout
      uses: actions/checkout@v3
      with:
        token: ${{ inputs.submodule-token }}
        submodules: true

    - name: Git submodule update
      run: |
        git pull --recurse-submodules
        git submodule update --remote --recursive
      shell: ${{ inputs.shell }}