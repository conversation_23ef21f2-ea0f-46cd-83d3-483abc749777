name: "Relative path"
description: "Relative path"
inputs:
  shell:
    required: true
    description: "Shell to use depending on runner os"

  fullPath:
    required: true
    description: "Full path to file"
  sedVariant:
    required: true
    description: "sed variant to use"

outputs:
  fileName:
    description: "Relative file name"
    value: ${{ steps.logName.outputs.fileName }}
  filePath:
    description: "Relative file path"
    value: ${{ steps.logPath.outputs.filePath }}

runs:
  using: "composite"
  steps:
    - name: Get relative path
      id: getRelativePath
      run: |
        pwd=$(pwd)
        echo "relativePath=$(echo ${{ inputs.fullPath }} | ${{ inputs.sedVariant }} "s^$pwd/^^g")" >> $GITHUB_OUTPUT
      shell: ${{ inputs.shell }}

    - name: Log path
      id: logPath
      run: |
        RESULT=$(dirname ${{ steps.getRelativePath.outputs.relativePath }})
        echo result: $RESULT
        echo "filePath=$RESULT" >> $GITHUB_OUTPUT
      shell: ${{ inputs.shell }}

    - name: Log name
      id: logName
      run: |
        RESULT=$(basename ${{ steps.getRelativePath.outputs.relativePath }})
        echo result: $RESULT
        echo "fileName=$RESULT" >> $GITHUB_OUTPUT
      shell: ${{ inputs.shell }}