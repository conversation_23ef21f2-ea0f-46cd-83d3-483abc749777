name: "Create file"
description: "Create file"
inputs:
  shell:
    required: true
    description: "Shell to use depending on runner os"

  filePath:
    description: "File path"
    required: true
  fileContent:
    description: "File content"
    required: true

runs:
  using: "composite"
  steps:
    - name: Create file
      run: |
        echo '${{ inputs.fileContent }}' >> ${{ inputs.filePath }}

      shell: ${{ inputs.shell }}