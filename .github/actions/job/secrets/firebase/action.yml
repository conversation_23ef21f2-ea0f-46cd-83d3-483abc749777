name: "Inject firebase secrets"
description: "Inject firebase secrets"
inputs:
  shell:
    required: true
    description: "Shell to use depending on runner os"
  sedVariant:
    required: true
    description: "Sed variant to use depending on runner os"

  firebaseKeylessApiKey:
    description: "Firebase api key"
    required: true
  firebaseKeylessProjectId:
    description: "Firebase keyless project id"
    required: true
  firebaseKeylessProjectNumber:
    description: "Firebase keyless project number"
    required: true
  firebaseKeylessStorageBucket:
    description: "Firebase keyless storage bucket"
    required: true

runs:
  using: "composite"
  steps:
    - name: Inject firebase secrets
      run: |
        cp app/google-services.json.templete app/google-services.json
        
        ${{ inputs.sedVariant }} -i 's/_PROJECT_NUMBER_/${{ inputs.firebaseKeylessProjectNumber }}/g' app/google-services.json
        ${{ inputs.sedVariant }} -i 's/_PROJECT_ID_/${{ inputs.firebaseKeylessProjectId }}/' app/google-services.json
        ${{ inputs.sedVariant }} -i 's/_STORAGE_BUCKET_/${{ inputs.firebaseKeylessStorageBucket }}/' app/google-services.json
        ${{ inputs.sedVariant }} -i 's/_API_KEY_/${{ inputs.firebaseKeylessApiKey }}/' app/google-services.json
      shell: ${{ inputs.shell }}