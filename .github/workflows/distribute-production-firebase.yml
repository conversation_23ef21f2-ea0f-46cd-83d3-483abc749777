concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

name: Distribute production firebase

on:

  workflow_dispatch:

jobs:
  build-android:
    name: Build release apk
    uses: ./.github/workflows/reusable-build-apk.yml
    secrets: inherit
    with:
      apkPath:  "app/build/outputs/apk/release/app-release.apk"
      assembleVariant: "Release"
      versionName: "Production"
      artifactUploadApkName: "built-apk"

      environment: "production"

  distribute-android:
    name: Distribute Android
    uses: ./.github/workflows/reusable-apk-distribution-firebase.yml
    secrets: inherit
    needs: build-android
    with:
      artifactDownloadApkName: "built-apk"
      apkFullPath: ${{ needs.build-android.outputs.apkPath }}/${{ needs.build-android.outputs.apkName }}
      workflowFileName: "distribute-production-firebase.yml"

      environment: "production"

#  build-ios:
#    name: Build release ipa
#    uses: ./.github/workflows/reusable-build-ipa.yml
#    secrets: inherit
#    with:
#      artifactUploadIpaName: "built-ipa"
#      artifactUploadDSYMName: "built-dsym"
#      versionName: "0.0.0"
#
#      environment: "staging"
#
#  distribute-ios:
#    name: Distribute Ios
#    uses: ./.github/workflows/reusable-ipa-distribution-staging.yml
#    secrets: inherit
#    needs: build-ios
#    with:
#      artifactDownloadIpaName: "built-ipa"
#      ipaFullPath: ${{ needs.build-ios.outputs.ipaPath }}/${{ needs.build-ios.outputs.ipaName }}
#
#      environment: "staging"

  remove-deployments:
    name: Remove auto deployments
    runs-on: ubuntu-22.04
    needs: distribute-android
    if: always()
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Delete deployments
        uses: ./.github/actions/job/deployments-delete

#  remove-ios-deployments:
#    name: Remove auto ios deployments
#    runs-on: ubuntu-22.04
#    needs: distribute-ios
#    if: always()
#    steps:
#      - name: Checkout
#        uses: actions/checkout@v3
#
#      - name: Delete deployments
#        uses: ./.github/actions/job/deployments-delete

  cancel-android-workflow:
    name: Cancel android workflow
    runs-on: ubuntu-22.04
    if: failure()
    needs: [build-android]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Delete deployments
        uses: ./.github/actions/job/cancel-action

#  cancel-ios-workflow:
#    name: Cancel ios workflow
#    runs-on: ubuntu-22.04
#    if: failure()
#    needs: [build-ios]
#    steps:
#      - name: Checkout
#        uses: actions/checkout@v3
#
#      - name: Delete deployments
#        uses: ./.github/actions/job/cancel-action

  cancel-android-distribute-workflow:
    name: Cancel android distribute workflow
    runs-on: ubuntu-22.04
    if: failure()
    needs: [distribute-android]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Delete deployments
        uses: ./.github/actions/job/cancel-action

#  cancel-ios-distribute-workflow:
#    name: Cancel ios distribute workflow
#    runs-on: ubuntu-22.04
#    if: failure()
#    needs: [distribute-ios]
#    steps:
#      - name: Checkout
#        uses: actions/checkout@v3
#
#      - name: Delete deployments
#        uses: ./.github/actions/job/cancel-action