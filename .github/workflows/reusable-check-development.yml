name: Project development checks

on:
  workflow_call:
    inputs:
      machine:
        required: true
        description: "Environment to build for"
        type: string
        default: "ubuntu-22.04"
      environment:
        required: true
        description: "Environment to build for"
        type: string

jobs:
  check:
    name: Check
    runs-on: ${{ inputs.machine }}
    env:
      sed: "sed"
      shell: "bash"
    environment:
      name: ${{ inputs.environment }}
    permissions:
      actions: read
      contents: read
      deployments: write
    outputs:
      apkPath: ${{ steps.uploadApk.outputs.filePath }}
      apkName: ${{ steps.uploadApk.outputs.fileName }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.SUBMODULE_ACCESS_TOKEN }}
          submodules: true

      - name: Inject project secrets
        uses: ./.github/actions/job/secrets
        with:
          environment: ${{ inputs.environment }}

          keylessBaseApiUrl: ${{ secrets.BASE_API_URL }}
          keylessBaseImageUrl: ${{ secrets.BASE_IMAGE_URL }}
          keylessForgotPasswordUrl: ${{ secrets.FORGOT_PASSWORD_URL }}

          releaseSigningKeyAlias: ${{ secrets.KEYLESS_ANDROID_SIGNING_ALIAS }}
          releaseSigningKeyPassword: ${{ secrets.KEYLESS_ANDROID_SIGNING_PASSWORD }}
          releaseSigningKeyStoreFile: "app/release.jks"
          releaseSigningKeyStoreFileContent: ${{ secrets.KEYLESS_ANDROID_SIGNING_KEYSTORE }}
          releaseSigningKeyStorePassword: ${{ secrets.KEYLESS_ANDROID_SIGNING_KEYSTORE_PASSWORD }}

          shell: ${{ env.shell }}
          sedVariant: ${{ env.sed }}

      - name: Inject android google service
        uses: ./.github/actions/job/file/create
        with:
          filePath: app/google-services.json
          fileContent: ${{ secrets.ANDROID_FIREBASE_GOOGLE_SERVICE }}

      - name: Init JDK
        uses: ./.github/actions/job/setup-jdk

      - name: Init cache
        uses: ./.github/actions/job/setup-cache

      - name: Init gradle
        uses: ./.github/actions/gradle/setup-gradle

      - name: All checks
        uses: ./.github/actions/gradle/check
        with:
          shell: ${{ env.shell }}