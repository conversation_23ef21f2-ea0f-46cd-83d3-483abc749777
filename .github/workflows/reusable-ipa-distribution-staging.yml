name: Testflight distribution

on:
  workflow_call:
    inputs:
      artifactDownloadIpaName:
        required: true
        description: "name of artifact upload"
        type: string
      ipaFullPath:
        required: true
        description: "Path to the ipa"
        type: string

      environment:
        required: true
        description: "Environment to build for"
        type: string

jobs:
  distribute:
    name: Distribute testflight
    runs-on: macos-latest
    env:
      shell: "bash"
      sed: "sed"
    environment: ${{ inputs.environment }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: "0"

      - name: Relative signed file path
        id: relativeAppPath
        uses: ./.github/actions/job/relative-path
        with:
          fullPath: ${{ inputs.ipaFullPath }}
          sedVariant: ${{ env.sed }}
          shell: ${{ env.shell }}

      - name: Download signed ipa
        id: ipaDownload
        uses: ./.github/actions/job/artifact-download
        with:
          downloadName: ${{ inputs.artifactDownloadIpaName }}
          downloadPath: ${{ steps.relativeAppPath.outputs.filePath }}

      - name: Generate release notes from workflow
        id: generateReleaseNotes
        uses: ./.github/actions/job/workflow-release-notes
        with:
          githubToken: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository }}
          sedVariant: ${{ env.sed }}
          workflowFileName: distribute-staging.yml
          shell: ${{ env.shell }}

      - name: Set up ruby env
        uses: ruby/setup-ruby@v1.138.0
        with:
          ruby-version: 3.2.1
          bundler-cache: true
          working-directory: ./ios

      - name: Distribute ipa
        working-directory: ./ios
        run: bundle exec fastlane ios testflight_release_staging
        env:
          ASC_KEY_ID: ${{ secrets.KEYLESS_ASC_KEY_ID }}
          ASC_ISSUER_ID: ${{ secrets.KEYLESS_ASC_ISSUER_ID }}
          ASC_KEY: ${{ secrets.KEYLESS_ASC_PRIVATE_KEY }}
          IPA_PATH: ${{ steps.relativeAppPath.outputs.fileName }}
          RELEASE_NOTES: "Staging"