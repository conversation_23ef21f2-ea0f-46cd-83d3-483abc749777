name: Build bundle

on:
  workflow_call:
    inputs:
      bundlePath:
        required: true
        description: "Path to the bundle"
        type: string
      mappingPath:
        required: true
        description: "Path to the mapping"
        type: string
      assembleVariant:
        required: true
        description: "Build variant to use"
        type: string
      environment:
        required: true
        description: "Environment to build for"
        type: string
      versionName:
        required: true
        description: "Version name to use"
        type: string

      artifactUploadBundleName:
        required: true
        description: "name of artifact upload"
        type: string
      artifactUploadBundleMappingName:
        required: true
        description: "name of artifact upload"
        type: string

    outputs:
      bundlePath:
        description: "Path to the bundle"
        value: ${{ jobs.build.outputs.bundlePath }}
      bundleName:
        description: "Name of the bundle"
        value: ${{ jobs.build.outputs.bundleName }}
      bundleMappingPath:
        description: "Path to the bundle mapping"
        value: ${{ jobs.build.outputs.bundleMappingPath }}
      bundleMappingName:
        description: "Name of the bundle mapping"
        value: ${{ jobs.build.outputs.bundleMappingName }}

jobs:
  build:
    name: Build
    runs-on: ubuntu-22.04
    env:
      sed: "sed"
      shell: "bash"
    environment:
      name: ${{ inputs.environment }}
    permissions:
      actions: read
      contents: read
      deployments: write
    outputs:
      bundlePath: ${{ steps.uploadBundle.outputs.filePath }}
      bundleName: ${{ steps.uploadBundle.outputs.fileName }}
      bundleMappingPath: ${{ steps.uploadBundleMapping.outputs.filePath }}
      bundleMappingName: ${{ steps.uploadBundleMapping.outputs.fileName }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.SUBMODULE_ACCESS_TOKEN }}
          submodules: true

      - name: Inject project secrets
        uses: ./.github/actions/job/secrets
        with:
          environment: ${{ inputs.environment }}

          keylessBaseApiUrl: ${{ secrets.BASE_API_URL }}
          keylessBaseImageUrl: ${{ secrets.BASE_IMAGE_URL }}
          keylessForgotPasswordUrl: ${{ secrets.FORGOT_PASSWORD_URL }}

          releaseSigningKeyAlias: ${{ secrets.KEYLESS_ANDROID_SIGNING_ALIAS }}
          releaseSigningKeyPassword: ${{ secrets.KEYLESS_ANDROID_SIGNING_PASSWORD }}
          releaseSigningKeyStoreFile: "app/release.jks"
          releaseSigningKeyStoreFileContent: ${{ secrets.KEYLESS_ANDROID_SIGNING_KEYSTORE }}
          releaseSigningKeyStorePassword: ${{ secrets.KEYLESS_ANDROID_SIGNING_KEYSTORE_PASSWORD }}

          shell: ${{ env.shell }}
          sedVariant: ${{ env.sed }}

      - name: Inject android google service
        uses: ./.github/actions/job/file/create
        with:
          filePath: app/google-services.json
          fileContent: ${{ secrets.ANDROID_FIREBASE_GOOGLE_SERVICE }}

      - name: Init JDK
        uses: ./.github/actions/job/setup-jdk

      - name: Init cache
        uses: ./.github/actions/job/setup-cache

      - name: Init gradle
        uses: ./.github/actions/gradle/setup-gradle

      - name: Init version name
        id: init-version-name
        uses: ./.github/actions/job/release-version
        with:
          refName: ${{ inputs.versionName }}
          shell: ${{ env.shell }}

      - name: Bump version code
        uses: ./.github/actions/gradle/bump-android-version
        with:
          versionCode: ${{ github.run_number }}
          versionName: ${{ steps.init-version-name.outputs.versionName }}
          gradlePath: "app/build.gradle.kts"

      - name: Assemble bundle
        uses: ./.github/actions/gradle/assemble/bundle
        with:
          shell: ${{ env.shell }}
          bundleVariant: ${{ inputs.assembleVariant }}

      - name: Upload built bundle
        id: uploadBundle
        uses: ./.github/actions/job/artifact-upload
        with:
          uploadName: ${{ inputs.artifactUploadBundleName }}
          uploadPath: ${{ inputs.bundlePath }}
          shell: ${{ env.shell }}

      - name: Upload built bundle mapping
        id: uploadBundleMapping
        uses: ./.github/actions/job/artifact-upload
        with:
          uploadName: ${{ inputs.artifactUploadBundleMappingName }}
          uploadPath: ${{ inputs.mappingPath }}
          shell: ${{ env.shell }}