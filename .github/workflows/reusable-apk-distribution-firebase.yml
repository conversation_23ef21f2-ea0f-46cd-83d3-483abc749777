name: Firebase distribution

on:
  workflow_call:
    inputs:
      artifactDownloadApkName:
        required: true
        description: "name of artifact upload"
        type: string
      apkFullPath:
        required: true
        description: "Path to the apk"
        type: string
      workflowFileName:
        required: true
        description: "Name of workflow file to generate release notes from"
        type: string

      environment:
        required: true
        description: "Environment to build for"
        type: string

jobs:
  distribute:
    name: Distribute firebase
    runs-on: ubuntu-22.04
    env:
      shell: "bash"
      sed: "sed"
    environment: ${{ inputs.environment }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: "0"

      - name: Relative signed file path
        id: relativeAppPath
        uses: ./.github/actions/job/relative-path
        with:
          fullPath: ${{ inputs.apkFullPath }}
          sedVariant: ${{ env.sed }}
          shell: ${{ env.shell }}

      - name: Download signed apk
        id: apkDownload
        uses: ./.github/actions/job/artifact-download
        with:
          downloadName: ${{ inputs.artifactDownloadApkName }}
          downloadPath: ${{ steps.relativeAppPath.outputs.filePath }}

      - name: Generate release notes from workflow
        id: generateReleaseNotes
        uses: ./.github/actions/job/workflow-release-notes
        with:
          githubToken: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository }}
          sedVariant: ${{ env.sed }}
          workflowFileName: ${{ inputs.workflowFileName }}
          shell: ${{ env.shell }}

      - name: Distribute
        uses: wzieba/Firebase-Distribution-Github-Action@v1.7.1
        with:
          appId: ${{ secrets.KEYLESS_ANDROID_FIREBASE_APPID }}
          serviceCredentialsFileContent: ${{ secrets.KEYLESS_FIREBASE_DISTRIBUTION_SERVICE }}
          groups: testers
          file: ${{ steps.relativeAppPath.outputs.filePath }}/${{ steps.relativeAppPath.outputs.fileName }}
          releaseNotesFile: workflow_release_notes.txt