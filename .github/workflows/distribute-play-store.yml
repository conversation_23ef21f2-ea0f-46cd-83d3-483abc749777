concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

name: Distribute play store

on:
  push:
    tags:
      - "[0-9]+.[0-9]+.[0-9]+.android"
      - "[0-9]+.[0-9]+.[0-9]+-rc[0-9]+.android"

  workflow_dispatch:

jobs:
  build:
    name: Build release bundle
    uses: ./.github/workflows/reusable-build-bundle.yml
    secrets: inherit
    with:
      artifactUploadBundleName: "built-bundle"
      bundlePath:  "app/build/outputs/bundle/release/app-release.aab"
      artifactUploadBundleMappingName: "built-bundle-mapping"
      mappingPath: "app/build/outputs/mapping/release/mapping.txt"
      assembleVariant: "Release"
      environment: "production"
      versionName: ${{ github.ref_name }}

  distribute:
    name: Distribute Google play
    uses: ./.github/workflows/reusable-apk-distribution-google.yml
    secrets: inherit
    needs: build
    with:
      artifactDownloadApkName: "built-bundle"
      artifactDownloadMappingName: "built-bundle-mapping"
      apkFullPath: ${{ needs.build.outputs.bundlePath }}/${{ needs.build.outputs.bundleName }}
      mappingFullPath: ${{ needs.build.outputs.bundleMappingPath }}/${{ needs.build.outputs.bundleMappingName }}
      track: "internal"
      environment: "production"

  remove-deployments:
    name: Remove auto deployments
    runs-on: ubuntu-22.04
    needs: distribute
    if: always()
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Delete deployments
        uses: ./.github/actions/job/deployments-delete