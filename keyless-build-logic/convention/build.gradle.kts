plugins {
    `kotlin-dsl`
}

java {
    sourceCompatibility = JavaVersion.toVersion(keyless.versions.javaCompatibility.get())
    targetCompatibility = JavaVersion.toVersion(keyless.versions.javaCompatibility.get())
}

dependencies {
    compileOnly(keyless.android.gradle.plugin)
    compileOnly(keyless.firebase.performance.gradle.plugin)
    compileOnly(keyless.firebase.crashlytics.gradle.plugin)
    compileOnly(keyless.kotlin.gradle.plugin)
    compileOnly(keyless.moko.resources.gradle.plugin)
    compileOnly(keyless.sqldelight.gradle.plugin)
}

gradlePlugin {
    plugins {
        register("keylessAndroidApplication") {
            id = "keyless.android.application"
            implementationClass = "AndroidApplicationConventionPlugin"
        }
        register("keylessAndroidCompose") {
            id = "keyless.android.compose"
            implementationClass = "AndroidComposeConventionPlugin"
        }
        register("keylessAndroidFeature") {
            id = "keyless.android.feature"
            implementationClass = "AndroidFeatureConventionPlugin"
        }
        register("keylessAndroidLibrary") {
            id = "keyless.android.library"
            implementationClass = "AndroidLibraryConventionPlugin"
        }
    }

    plugins {
        register("keylessKotlin") {
            id = "keyless.kotlin"
            implementationClass = "KotlinModuleConventionPlugin"
        }
        register("keylessKotlinxSerialization") {
            id = "kotlinx.serialization"
            implementationClass = "KotlinxSerializationConventionPlugin"
        }
    }

    plugins {
        register("keylessKoin") {
            id = "keyless.koin"
            implementationClass = "KoinConventionPlugin"
        }
        register("keylessKoinAndroid") {
            id = "keyless.koin.android"
            implementationClass = "KoinAndroidConventionPlugin"
        }
        register("keylessSqlDelight") {
            id = "keyless.sqldelight"
            implementationClass = "SqlDelightConventionPlugin"
        }
    }

    plugins {
        register("configValues") {
            id = "config.values"
            implementationClass = "ConfigValuesConventionPlugin"
        }
    }
}