@file:Suppress("PackageDirectoryMismatch")

import org.gradle.api.Plugin
import org.gradle.api.Project

class KotlinxSerializationConventionPlugin : Plugin<Project> {

    override fun apply(target: Project): Unit = with(target) {
        pluginManager.apply("org.jetbrains.kotlin.plugin.serialization")

        dependencies.add("implementation", catalog.findLibrary("kotlinx.serialization.json").get())
    }
}