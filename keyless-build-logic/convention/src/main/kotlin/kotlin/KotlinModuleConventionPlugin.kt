@file:Suppress("PackageDirectoryMismatch", "unused")

import org.gradle.api.JavaVersion
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.plugins.JavaPluginExtension
import org.gradle.kotlin.dsl.configure

class KotlinModuleConventionPlugin : Plugin<Project> {
    override fun apply(target: Project): Unit = with(target) {
        pluginManager.apply("org.jetbrains.kotlin.jvm")
        pluginManager.apply("kotlinx.serialization")

        dependencies.add("implementation", catalog.findLibrary("kotlinx.coroutines.core").get())
        dependencies.add("implementation", catalog.findLibrary("kotlinx.datetime").get())

        dependencies.add("testImplementation", catalog.findLibrary("kotlin.test").get())
        dependencies.add("testImplementation", catalog.findLibrary("kotlinx.coroutines.test").get())

        extensions.configure<JavaPluginExtension>() {
            sourceCompatibility = JavaVersion.toVersion(catalog.findVersion("javaSource").get().displayName)
            targetCompatibility = JavaVersion.toVersion(catalog.findVersion("javaTarget").get().displayName)
        }
    }
}